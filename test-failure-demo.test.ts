/**
 * Temporary test to demonstrate error reporting with silent mode
 */

describe('Error Reporting Demo', () => {
  it('should show full stack trace when test fails', () => {
    const expected = 'hello';
    const actual = 'world';
    
    // This will fail and show full error details
    expect(actual).toBe(expected);
  });

  it('should show detailed assertion errors', () => {
    const obj = { name: '<PERSON>', age: 30 };
    const expected = { name: '<PERSON>', age: 25 };
    
    // This will fail with detailed object comparison
    expect(obj).toEqual(expected);
  });

  it('should show stack trace for thrown errors', () => {
    function problematicFunction() {
      throw new Error('Something went wrong in the function');
    }
    
    // This will fail and show the full stack trace
    expect(() => problematicFunction()).not.toThrow();
  });
});
