/**
 * Tests for the base AppError class and concrete implementations
 */

import { AppError } from '../../src/errors/app-error';
import { ValidationError } from '../../src/errors/validation-error';
import { NetworkError } from '../../src/errors/network-error';
import { AuthenticationError } from '../../src/errors/authentication-error';
import { ApplicationError } from '../../src/errors/application-error';
import { CryptoError } from '../../src/errors/crypto-error';
import { FileOperationError } from '../../src/errors/file-operation-error';

describe('AppError System', () => {
  describe('ValidationError', () => {
    it('should create a validation error with user message', () => {
      const error = new ValidationError('Internal message', 'User-friendly message');
      expect(error.message).toBe('Internal message');
      expect(error.userMessage).toBe('User-friendly message');
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.exitCode).toBe(2);
      expect(error.name).toBe('ValidationError');
    });

    it('should create validation error with details', () => {
      const details = { field: 'username', value: '' };
      const error = new ValidationError('Internal message', 'User message', details);
      expect(error.details).toEqual(details);
    });

    it('should create validation error with static methods', () => {
      const error = ValidationError.missingRequired('username');
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.userMessage).toContain('username');
      expect(error.message).toContain('username');
    });

    it('should be an instance of Error and AppError', () => {
      const error = new ValidationError('Test', 'Test message');
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(AppError);
      expect(error).toBeInstanceOf(ValidationError);
    });

    it('should have a stack trace', () => {
      const error = new ValidationError('Test', 'Test message');
      expect(error.stack).toBeDefined();
      expect(typeof error.stack).toBe('string');
    });

    it('should serialize to JSON correctly', () => {
      const error = new ValidationError('Internal', 'User message', { field: 'test' });
      const json = error.toJSON();

      expect(json).toEqual({
        name: 'ValidationError',
        code: 'VALIDATION_ERROR',
        message: 'Internal',
        userMessage: 'User message',
        exitCode: 2,
        details: { field: 'test' }
      });
    });
  });

  describe('NetworkError', () => {
    it('should create a network error', () => {
      const error = new NetworkError('Connection failed', 'Network is unavailable');
      expect(error.message).toBe('Connection failed');
      expect(error.userMessage).toBe('Network is unavailable');
      expect(error.code).toBe('NETWORK_ERROR');
      expect(error.exitCode).toBe(2);
      expect(error.name).toBe('NetworkError');
    });

    it('should create network error with default user message', () => {
      const error = new NetworkError('Network failed');
      expect(error.userMessage).toBe('A network operation failed. Please check your internet connection.');
    });

    it('should create network error with URL and status code', () => {
      const error = new NetworkError('HTTP error', 'Server error', 'https://api.example.com', 500);
      expect(error.url).toBe('https://api.example.com');
      expect(error.statusCode).toBe(500);
      expect(error.details).toEqual({ url: 'https://api.example.com', statusCode: 500 });
    });

    it('should create network error with additional details', () => {
      const details = { retryCount: 3, timeout: 5000 };
      const error = new NetworkError('Request failed', 'Custom message', 'https://api.example.com', 404, details);
      expect(error.details).toEqual({
        retryCount: 3,
        timeout: 5000,
        url: 'https://api.example.com',
        statusCode: 404
      });
    });

    it('should be an instance of Error and AppError', () => {
      const error = new NetworkError('Test', 'Test message');
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(AppError);
      expect(error).toBeInstanceOf(NetworkError);
    });

    describe('Factory methods', () => {
      it('should create connection failed error', () => {
        const error = NetworkError.connectionFailed('https://api.example.com');
        expect(error.message).toBe('Failed to connect to https://api.example.com');
        expect(error.userMessage).toBe('Failed to connect to server. Please check your internet connection.');
        expect(error.url).toBe('https://api.example.com');
        expect(error.statusCode).toBeUndefined();
      });

      it('should create connection failed error with original error', () => {
        const originalError = new Error('ECONNREFUSED');
        const error = NetworkError.connectionFailed('https://api.example.com', originalError);
        expect(error.details).toEqual({
          originalError: 'ECONNREFUSED',
          url: 'https://api.example.com',
          statusCode: undefined
        });
      });

      it('should create timeout error', () => {
        const error = NetworkError.timeout('https://api.example.com', 5000);
        expect(error.message).toBe('Request to https://api.example.com timed out after 5000ms');
        expect(error.userMessage).toBe('The server took too long to respond. Please try again later.');
        expect(error.url).toBe('https://api.example.com');
        expect(error.details).toEqual({
          timeoutMs: 5000,
          url: 'https://api.example.com',
          statusCode: undefined
        });
      });

      it('should create HTTP error with 4xx status', () => {
        const error = NetworkError.httpError('https://api.example.com', 404);
        expect(error.message).toBe('HTTP error 404 from https://api.example.com');
        expect(error.userMessage).toBe('The requested resource was not found on the server.');
        expect(error.statusCode).toBe(404);
      });

      it('should create HTTP error with 5xx status', () => {
        const error = NetworkError.httpError('https://api.example.com', 500);
        expect(error.message).toBe('HTTP error 500 from https://api.example.com');
        expect(error.userMessage).toBe('The server encountered an error. Please try again later.');
        expect(error.statusCode).toBe(500);
      });

      it('should create HTTP error with 401 status', () => {
        const error = NetworkError.httpError('https://api.example.com', 401);
        expect(error.userMessage).toBe('Authentication failed. Please check your credentials or login again.');
      });

      it('should create HTTP error with 403 status', () => {
        const error = NetworkError.httpError('https://api.example.com', 403);
        expect(error.userMessage).toBe('Authentication failed. Please check your credentials or login again.');
      });

      it('should create HTTP error with 503 status', () => {
        const error = NetworkError.httpError('https://api.example.com', 503);
        expect(error.userMessage).toBe('The server is temporarily unavailable. Please try again later.');
      });

      it('should create HTTP error with response data', () => {
        const responseData = { error: 'Invalid request', code: 'INVALID_PARAM' };
        const error = NetworkError.httpError('https://api.example.com', 400, responseData);
        expect(error.details).toEqual({
          responseData,
          url: 'https://api.example.com',
          statusCode: 400
        });
      });

      it('should create HTTP error with unknown status code', () => {
        const error = NetworkError.httpError('https://api.example.com', 999);
        expect(error.userMessage).toBe('An error occurred while communicating with the server.');
      });

      it('should create API error', () => {
        const error = NetworkError.apiError('https://api.example.com', 400, 'INVALID_PARAM', 'Parameter is invalid');
        expect(error.message).toBe('API error: Parameter is invalid');
        expect(error.userMessage).toBe('Parameter is invalid');
        expect(error.url).toBe('https://api.example.com');
        expect(error.statusCode).toBe(400);
        expect(error.details).toEqual({
          errorCode: 'INVALID_PARAM',
          url: 'https://api.example.com',
          statusCode: 400
        });
      });

      it('should create API error with default message', () => {
        const error = NetworkError.apiError('https://api.example.com', 500);
        expect(error.message).toBe('API error: Unknown error');
        expect(error.userMessage).toBe('The server returned an error response.');
        expect(error.details).toEqual({
          errorCode: undefined,
          url: 'https://api.example.com',
          statusCode: 500
        });
      });
    });
  });

  describe('AuthenticationError', () => {
    it('should create an authentication error', () => {
      const error = new AuthenticationError('Invalid token', 'Please check your credentials');
      expect(error.message).toBe('Invalid token');
      expect(error.userMessage).toBe('Please check your credentials');
      expect(error.code).toBe('AUTH_ERROR');
      expect(error.exitCode).toBe(1);
      expect(error.name).toBe('AuthenticationError');
    });

    it('should create authentication error with default user message', () => {
      const error = new AuthenticationError('Auth failed');
      expect(error.userMessage).toBe('Authentication failed. Please check your credentials.');
    });

    it('should create authentication error with details', () => {
      const details = { endpoint: '/login', statusCode: 401 };
      const error = new AuthenticationError('Auth failed', 'Custom message', details);
      expect(error.details).toEqual(details);
    });

    it('should be an instance of Error and AppError', () => {
      const error = new AuthenticationError('Test', 'Test message');
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(AppError);
      expect(error).toBeInstanceOf(AuthenticationError);
    });

    describe('Factory methods', () => {
      it('should create invalid password error', () => {
        const error = AuthenticationError.invalidPassword();
        expect(error.message).toBe('Invalid password provided');
        expect(error.userMessage).toBe('The password you entered is incorrect. Please try again.');
        expect(error.code).toBe('AUTH_ERROR');
      });

      it('should create invalid password error with custom message', () => {
        const error = AuthenticationError.invalidPassword('Custom password error');
        expect(error.message).toBe('Custom password error');
        expect(error.userMessage).toBe('The password you entered is incorrect. Please try again.');
      });

      it('should create password required error', () => {
        const error = AuthenticationError.passwordRequired();
        expect(error.message).toBe('Password required to decrypt master key');
        expect(error.userMessage).toBe('A password is required to decrypt your secure data. Please provide your password.');
      });

      it('should create invalid token error', () => {
        const error = AuthenticationError.invalidToken();
        expect(error.message).toBe('Invalid or expired authentication token');
        expect(error.userMessage).toBe('Your authentication token is invalid or has expired. Please log in again.');
      });

      it('should create invalid token error with details', () => {
        const details = { tokenType: 'Bearer', expiresAt: '2023-01-01' };
        const error = AuthenticationError.invalidToken(details);
        expect(error.details).toEqual(details);
      });

      it('should create invalid session error', () => {
        const error = AuthenticationError.invalidSession();
        expect(error.message).toBe('Invalid or expired session');
        expect(error.userMessage).toBe('Your session has expired. Please log in again.');
      });

      it('should create invalid session error with details', () => {
        const details = { sessionId: 'abc123', expiresAt: '2023-01-01' };
        const error = AuthenticationError.invalidSession(details);
        expect(error.details).toEqual(details);
      });
    });
  });

  describe('CryptoError', () => {
    it('should create a crypto error', () => {
      const error = new CryptoError('Encryption failed', 'Unable to encrypt data');
      expect(error.message).toBe('Encryption failed');
      expect(error.userMessage).toBe('Unable to encrypt data');
      expect(error.code).toBe('CRYPTO_ERROR');
      expect(error.exitCode).toBe(3);
      expect(error.name).toBe('CryptoError');
    });

    it('should create crypto error with details', () => {
      const details = { algorithm: 'AES-256-GCM', operation: 'encrypt' };
      const error = new CryptoError('Encryption failed', 'Unable to encrypt data', details);
      expect(error.details).toEqual(details);
    });

    it('should be an instance of Error and AppError', () => {
      const error = new CryptoError('Test', 'Test message');
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(AppError);
      expect(error).toBeInstanceOf(CryptoError);
    });

    describe('Factory methods', () => {
      it('should create key not initialized error', () => {
        const error = CryptoError.keyNotInitialized();
        expect(error.message).toBe('Encryption key not initialized');
        expect(error.userMessage).toBe('The encryption key has not been initialized. Please provide a password.');
        expect(error.code).toBe('CRYPTO_ERROR');
      });

      it('should create key not initialized error with details', () => {
        const details = { keyType: 'master', required: true };
        const error = CryptoError.keyNotInitialized(details);
        expect(error.details).toEqual(details);
      });

      it('should create invalid password error', () => {
        const error = CryptoError.invalidPassword();
        expect(error.message).toBe('Invalid password or corrupted key file');
        expect(error.userMessage).toBe('The provided password is incorrect or the key file is corrupted.');
      });

      it('should create invalid password error with details', () => {
        const details = { attempts: 3, maxAttempts: 5 };
        const error = CryptoError.invalidPassword(details);
        expect(error.details).toEqual(details);
      });

      it('should create decryption failed error', () => {
        const error = CryptoError.decryptionFailed();
        expect(error.message).toBe('Failed to decrypt data');
        expect(error.userMessage).toBe('Failed to decrypt the data. The data may be corrupted or the encryption key is invalid.');
      });

      it('should create decryption failed error with details', () => {
        const details = { dataSize: 1024, algorithm: 'AES-256-GCM' };
        const error = CryptoError.decryptionFailed(details);
        expect(error.details).toEqual(details);
      });

      it('should create encryption failed error', () => {
        const error = CryptoError.encryptionFailed();
        expect(error.message).toBe('Failed to encrypt data');
        expect(error.userMessage).toBe('Failed to encrypt the data.');
      });

      it('should create encryption failed error with details', () => {
        const details = { dataSize: 2048, algorithm: 'AES-256-GCM' };
        const error = CryptoError.encryptionFailed(details);
        expect(error.details).toEqual(details);
      });
    });
  });

  describe('FileOperationError', () => {
    it('should create a file operation error', () => {
      const error = new FileOperationError('File read failed', 'Cannot read file', '/path/to/file.txt');
      expect(error.message).toBe('File read failed');
      expect(error.userMessage).toBe('Cannot read file');
      expect(error.filePath).toBe('/path/to/file.txt');
      expect(error.code).toBe('FILE_OPERATION_ERROR');
      expect(error.exitCode).toBe(4);
      expect(error.name).toBe('FileOperationError');
    });

    it('should create file operation error with details', () => {
      const details = { operation: 'read', size: 1024 };
      const error = new FileOperationError('File read failed', 'Cannot read file', '/path/to/file.txt', details);
      expect(error.details).toEqual(details);
    });

    it('should be an instance of Error and AppError', () => {
      const error = new FileOperationError('Test', 'Test message', '/test/path');
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(AppError);
      expect(error).toBeInstanceOf(FileOperationError);
    });

    describe('Factory methods', () => {
      it('should create not found error', () => {
        const error = FileOperationError.notFound('/path/to/missing.txt');
        expect(error.message).toBe('File not found: /path/to/missing.txt');
        expect(error.userMessage).toBe('The file "/path/to/missing.txt" was not found.');
        expect(error.filePath).toBe('/path/to/missing.txt');
        expect(error.code).toBe('FILE_OPERATION_ERROR');
      });

      it('should create not found error with details', () => {
        const details = { searchPaths: ['/path1', '/path2'] };
        const error = FileOperationError.notFound('/path/to/missing.txt', details);
        expect(error.details).toEqual(details);
      });

      it('should create permission denied error', () => {
        const error = FileOperationError.permissionDenied('/path/to/protected.txt');
        expect(error.message).toBe('Permission denied: /path/to/protected.txt');
        expect(error.userMessage).toBe('Permission denied for file "/path/to/protected.txt". Please check file permissions.');
        expect(error.filePath).toBe('/path/to/protected.txt');
      });

      it('should create permission denied error with details', () => {
        const details = { requiredPermissions: 'read', currentPermissions: 'none' };
        const error = FileOperationError.permissionDenied('/path/to/protected.txt', details);
        expect(error.details).toEqual(details);
      });

      it('should create read error', () => {
        const error = FileOperationError.readError('/path/to/file.txt');
        expect(error.message).toBe('Failed to read file: /path/to/file.txt');
        expect(error.userMessage).toBe('Failed to read file "/path/to/file.txt". Please check if the file exists and is accessible.');
        expect(error.filePath).toBe('/path/to/file.txt');
      });

      it('should create read error with original error', () => {
        const originalError = new Error('ENOENT: no such file or directory');
        const error = FileOperationError.readError('/path/to/file.txt', originalError);
        expect(error.details).toEqual({ originalError: 'ENOENT: no such file or directory' });
      });

      it('should create read error with details', () => {
        const details = { encoding: 'utf8', size: 2048 };
        const error = FileOperationError.readError('/path/to/file.txt', undefined, details);
        expect(error.details).toEqual({ originalError: undefined, encoding: 'utf8', size: 2048 });
      });

      it('should create write error', () => {
        const error = FileOperationError.writeError('/path/to/file.txt');
        expect(error.message).toBe('Failed to write file: /path/to/file.txt');
        expect(error.userMessage).toBe('Failed to write to file "/path/to/file.txt". Please check disk space and permissions.');
        expect(error.filePath).toBe('/path/to/file.txt');
      });

      it('should create directory creation error', () => {
        const error = FileOperationError.directoryCreationError('/path/to/dir');
        expect(error.message).toBe('Failed to create directory: /path/to/dir');
        expect(error.userMessage).toBe('Failed to create directory "/path/to/dir". Please check permissions.');
        expect(error.filePath).toBe('/path/to/dir');
      });

      it('should create directory creation error with original error and details', () => {
        const originalError = new Error('EACCES: permission denied');
        const details = { parentExists: true };
        const error = FileOperationError.directoryCreationError('/path/to/dir', originalError, details);
        expect(error.details).toEqual({ originalError: 'EACCES: permission denied', parentExists: true });
      });

      it('should create write error with original error and details', () => {
        const originalError = new Error('ENOSPC: no space left on device');
        const details = { dataSize: 1024 };
        const error = FileOperationError.writeError('/path/to/file.txt', originalError, details);
        expect(error.details).toEqual({ originalError: 'ENOSPC: no space left on device', dataSize: 1024 });
      });
    });
  });

  describe('ApplicationError', () => {
    it('should create an application error', () => {
      const error = new ApplicationError('Something went wrong', 'An unexpected error occurred');
      expect(error.message).toBe('Something went wrong');
      expect(error.userMessage).toBe('An unexpected error occurred');
      expect(error.code).toBe('APPLICATION_ERROR');
      expect(error.exitCode).toBe(1);
      expect(error.name).toBe('ApplicationError');
    });

    it('should be an instance of Error and AppError', () => {
      const error = new ApplicationError('Test', 'Test message');
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(AppError);
      expect(error).toBeInstanceOf(ApplicationError);
    });
  });

  describe('Error inheritance and polymorphism', () => {
    it('should work with instanceof checks', () => {
      const validationError = new ValidationError('Test', 'Test');
      const networkError = new NetworkError('Test', 'Test');
      const authError = new AuthenticationError('Test', 'Test');
      const appError = new ApplicationError('Test', 'Test');

      // All should be instances of AppError
      expect(validationError).toBeInstanceOf(AppError);
      expect(networkError).toBeInstanceOf(AppError);
      expect(authError).toBeInstanceOf(AppError);
      expect(appError).toBeInstanceOf(AppError);

      // All should be instances of Error
      expect(validationError).toBeInstanceOf(Error);
      expect(networkError).toBeInstanceOf(Error);
      expect(authError).toBeInstanceOf(Error);
      expect(appError).toBeInstanceOf(Error);
    });

    it('should have different exit codes', () => {
      const validationError = new ValidationError('Test', 'Test');
      const networkError = new NetworkError('Test', 'Test');
      const authError = new AuthenticationError('Test', 'Test');
      const appError = new ApplicationError('Test', 'Test');

      expect(validationError.exitCode).toBe(2);
      expect(networkError.exitCode).toBe(2);
      expect(authError.exitCode).toBe(1);
      expect(appError.exitCode).toBe(1);
    });

    it('should have different error codes', () => {
      const validationError = new ValidationError('Test', 'Test');
      const networkError = new NetworkError('Test', 'Test');
      const authError = new AuthenticationError('Test', 'Test');
      const appError = new ApplicationError('Test', 'Test');

      expect(validationError.code).toBe('VALIDATION_ERROR');
      expect(networkError.code).toBe('NETWORK_ERROR');
      expect(authError.code).toBe('AUTH_ERROR');
      expect(appError.code).toBe('APPLICATION_ERROR');
    });

    it('should handle error details correctly', () => {
      const details = { context: 'test', timestamp: Date.now() };
      const error = new ValidationError('Test', 'Test message', details);

      expect(error.details).toEqual(details);

      const json = error.toJSON();
      expect(json.details).toEqual(details);
    });

    it('should handle errors without details', () => {
      const error = new ValidationError('Test', 'Test message');

      expect(error.details).toBeUndefined();

      const json = error.toJSON();
      expect(json.details).toBeUndefined();
    });
  });

  describe('Static factory methods', () => {
    it('should create validation errors with static methods', () => {
      const missingError = ValidationError.missingRequired('username');
      expect(missingError.code).toBe('VALIDATION_ERROR');
      expect(missingError.message).toContain('username');
      expect(missingError.userMessage).toContain('username');

      const invalidError = ValidationError.invalidValue('age', -5);
      expect(invalidError.code).toBe('VALIDATION_ERROR');
      expect(invalidError.message).toContain('age');
      expect(invalidError.details?.value).toBe(-5);

      const formatError = ValidationError.invalidFormat('email');
      expect(formatError.code).toBe('VALIDATION_ERROR');
      expect(formatError.message).toContain('email');
    });
  });
});