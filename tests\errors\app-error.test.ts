/**
 * Tests for the base AppError class and concrete implementations
 */

import { AppError } from '../../src/errors/app-error';
import { ValidationError } from '../../src/errors/validation-error';
import { NetworkError } from '../../src/errors/network-error';
import { AuthenticationError } from '../../src/errors/authentication-error';
import { ApplicationError } from '../../src/errors/application-error';

describe('AppError System', () => {
  describe('ValidationError', () => {
    it('should create a validation error with user message', () => {
      const error = new ValidationError('Internal message', 'User-friendly message');
      expect(error.message).toBe('Internal message');
      expect(error.userMessage).toBe('User-friendly message');
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.exitCode).toBe(2);
      expect(error.name).toBe('ValidationError');
    });

    it('should create validation error with details', () => {
      const details = { field: 'username', value: '' };
      const error = new ValidationError('Internal message', 'User message', details);
      expect(error.details).toEqual(details);
    });

    it('should create validation error with static methods', () => {
      const error = ValidationError.missingRequired('username');
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.userMessage).toContain('username');
      expect(error.message).toContain('username');
    });

    it('should be an instance of Error and AppError', () => {
      const error = new ValidationError('Test', 'Test message');
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(AppError);
      expect(error).toBeInstanceOf(ValidationError);
    });

    it('should have a stack trace', () => {
      const error = new ValidationError('Test', 'Test message');
      expect(error.stack).toBeDefined();
      expect(typeof error.stack).toBe('string');
    });

    it('should serialize to JSON correctly', () => {
      const error = new ValidationError('Internal', 'User message', { field: 'test' });
      const json = error.toJSON();
      
      expect(json).toEqual({
        name: 'ValidationError',
        code: 'VALIDATION_ERROR',
        message: 'Internal',
        userMessage: 'User message',
        exitCode: 2,
        details: { field: 'test' }
      });
    });
  });

  describe('NetworkError', () => {
    it('should create a network error', () => {
      const error = new NetworkError('Connection failed', 'Network is unavailable');
      expect(error.message).toBe('Connection failed');
      expect(error.userMessage).toBe('Network is unavailable');
      expect(error.code).toBe('NETWORK_ERROR');
      expect(error.exitCode).toBe(2);
      expect(error.name).toBe('NetworkError');
    });

    it('should be an instance of Error and AppError', () => {
      const error = new NetworkError('Test', 'Test message');
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(AppError);
      expect(error).toBeInstanceOf(NetworkError);
    });
  });

  describe('AuthenticationError', () => {
    it('should create an authentication error', () => {
      const error = new AuthenticationError('Invalid token', 'Please check your credentials');
      expect(error.message).toBe('Invalid token');
      expect(error.userMessage).toBe('Please check your credentials');
      expect(error.code).toBe('AUTH_ERROR');
      expect(error.exitCode).toBe(1);
      expect(error.name).toBe('AuthenticationError');
    });

    it('should be an instance of Error and AppError', () => {
      const error = new AuthenticationError('Test', 'Test message');
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(AppError);
      expect(error).toBeInstanceOf(AuthenticationError);
    });
  });

  describe('ApplicationError', () => {
    it('should create an application error', () => {
      const error = new ApplicationError('Something went wrong', 'An unexpected error occurred');
      expect(error.message).toBe('Something went wrong');
      expect(error.userMessage).toBe('An unexpected error occurred');
      expect(error.code).toBe('APPLICATION_ERROR');
      expect(error.exitCode).toBe(1);
      expect(error.name).toBe('ApplicationError');
    });

    it('should be an instance of Error and AppError', () => {
      const error = new ApplicationError('Test', 'Test message');
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(AppError);
      expect(error).toBeInstanceOf(ApplicationError);
    });
  });

  describe('Error inheritance and polymorphism', () => {
    it('should work with instanceof checks', () => {
      const validationError = new ValidationError('Test', 'Test');
      const networkError = new NetworkError('Test', 'Test');
      const authError = new AuthenticationError('Test', 'Test');
      const appError = new ApplicationError('Test', 'Test');

      // All should be instances of AppError
      expect(validationError).toBeInstanceOf(AppError);
      expect(networkError).toBeInstanceOf(AppError);
      expect(authError).toBeInstanceOf(AppError);
      expect(appError).toBeInstanceOf(AppError);

      // All should be instances of Error
      expect(validationError).toBeInstanceOf(Error);
      expect(networkError).toBeInstanceOf(Error);
      expect(authError).toBeInstanceOf(Error);
      expect(appError).toBeInstanceOf(Error);
    });

    it('should have different exit codes', () => {
      const validationError = new ValidationError('Test', 'Test');
      const networkError = new NetworkError('Test', 'Test');
      const authError = new AuthenticationError('Test', 'Test');
      const appError = new ApplicationError('Test', 'Test');

      expect(validationError.exitCode).toBe(2);
      expect(networkError.exitCode).toBe(2);
      expect(authError.exitCode).toBe(1);
      expect(appError.exitCode).toBe(1);
    });

    it('should have different error codes', () => {
      const validationError = new ValidationError('Test', 'Test');
      const networkError = new NetworkError('Test', 'Test');
      const authError = new AuthenticationError('Test', 'Test');
      const appError = new ApplicationError('Test', 'Test');

      expect(validationError.code).toBe('VALIDATION_ERROR');
      expect(networkError.code).toBe('NETWORK_ERROR');
      expect(authError.code).toBe('AUTH_ERROR');
      expect(appError.code).toBe('APPLICATION_ERROR');
    });

    it('should handle error details correctly', () => {
      const details = { context: 'test', timestamp: Date.now() };
      const error = new ValidationError('Test', 'Test message', details);
      
      expect(error.details).toEqual(details);
      
      const json = error.toJSON();
      expect(json.details).toEqual(details);
    });

    it('should handle errors without details', () => {
      const error = new ValidationError('Test', 'Test message');
      
      expect(error.details).toBeUndefined();
      
      const json = error.toJSON();
      expect(json.details).toBeUndefined();
    });
  });

  describe('Static factory methods', () => {
    it('should create validation errors with static methods', () => {
      const missingError = ValidationError.missingRequired('username');
      expect(missingError.code).toBe('VALIDATION_ERROR');
      expect(missingError.message).toContain('username');
      expect(missingError.userMessage).toContain('username');

      const invalidError = ValidationError.invalidValue('age', -5);
      expect(invalidError.code).toBe('VALIDATION_ERROR');
      expect(invalidError.message).toContain('age');
      expect(invalidError.details?.value).toBe(-5);

      const formatError = ValidationError.invalidFormat('email');
      expect(formatError.code).toBe('VALIDATION_ERROR');
      expect(formatError.message).toContain('email');
    });
  });
});