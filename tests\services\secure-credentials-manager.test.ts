/**
 * Tests for SecureCredentialsManager
 */

import { SecureCredentialsManager, ConfluenceCredential } from '../../src/services/secure-credentials-manager';
import { CryptoUtils } from '../../src/utils/crypto-utils';
import { FileUtils } from '../../src/utils/file-utils';
import { CryptoError, ValidationError } from '../../src/errors';
import { defaultConfig } from '../../src/config/app-config';
import fs from 'fs/promises';
import path from 'path';

// Mock dependencies
jest.mock('../../src/utils/crypto-utils');
jest.mock('../../src/utils/file-utils');
jest.mock('../../src/utils/logger');

const mockCryptoUtils = CryptoUtils as jest.Mocked<typeof CryptoUtils>;
const mockFileUtils = FileUtils as jest.Mocked<typeof FileUtils>;

describe('SecureCredentialsManager', () => {
  let credentialsManager: SecureCredentialsManager;
  let testDir: string;
  let keyFilePath: string;
  let credentialsFilePath: string;

  beforeEach(async () => {
    // Create temporary test directory
    testDir = path.join(__dirname, 'temp-credentials-test');
    await fs.mkdir(testDir, { recursive: true });
    
    keyFilePath = path.join(testDir, 'master.key');
    credentialsFilePath = path.join(testDir, 'credentials.json');

    // Mock config paths
    jest.spyOn(defaultConfig.paths, 'keyFile', 'get').mockReturnValue(keyFilePath);
    jest.spyOn(defaultConfig.paths, 'credentialsFile', 'get').mockReturnValue(credentialsFilePath);
    jest.spyOn(defaultConfig.paths, 'configDir', 'get').mockReturnValue(testDir);

    // Reset mocks
    jest.clearAllMocks();

    // Setup default mock implementations
    mockFileUtils.ensureDirectory.mockResolvedValue();
    mockFileUtils.fileExists.mockResolvedValue(false);
    mockCryptoUtils.generateKey.mockReturnValue(Buffer.from('test-key-32-bytes-long-for-aes256'));
    mockCryptoUtils.encryptMasterKey.mockReturnValue({
      salt: 'test-salt',
      iv: 'test-iv',
      encryptedKey: 'encrypted-key-data',
      encrypted: true
    });
    mockCryptoUtils.encryptData.mockReturnValue({
      salt: 'test-salt',
      iv: 'test-iv',
      encryptedData: 'test-encrypted-data'
    });
    mockCryptoUtils.decryptData.mockReturnValue('{"credentials":[],"version":"2.0"}');
    mockCryptoUtils.secureWipe.mockImplementation(() => {});

    credentialsManager = new SecureCredentialsManager();
  });

  afterEach(async () => {
    // Cleanup
    try {
      await credentialsManager.cleanup();
      await fs.rm(testDir, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Constructor', () => {
    it('should create instance with correct file paths', () => {
      expect(credentialsManager).toBeInstanceOf(SecureCredentialsManager);
    });
  });

  describe('initialize', () => {
    it('should create new key file when none exists', async () => {
      mockFileUtils.fileExists.mockResolvedValue(false);
      mockFileUtils.writeJsonFile.mockResolvedValue();
      mockFileUtils.writeFile.mockResolvedValue();

      await credentialsManager.initialize('test-password');

      expect(mockCryptoUtils.generateKey).toHaveBeenCalled();
      expect(mockCryptoUtils.encryptMasterKey).toHaveBeenCalled();
      expect(mockFileUtils.writeJsonFile).toHaveBeenCalledWith(
        keyFilePath,
        expect.objectContaining({ encrypted: true }),
        { mode: 0o600 }
      );
    });

    it('should create plain key file when no password provided', async () => {
      mockFileUtils.fileExists.mockResolvedValue(false);
      mockFileUtils.writeFile.mockResolvedValue();

      await credentialsManager.initialize();

      expect(mockCryptoUtils.generateKey).toHaveBeenCalled();
      expect(mockFileUtils.writeFile).toHaveBeenCalledWith(
        keyFilePath,
        expect.any(Buffer),
        { mode: 0o600 }
      );
    });

    it('should load existing encrypted key file', async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      const encryptedMasterKey = {
        salt: 'test-salt',
        iv: 'test-iv',
        encryptedKey: 'encrypted-key-data',
        encrypted: true
      };
      
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue(JSON.stringify(encryptedMasterKey));
      mockCryptoUtils.decryptMasterKey.mockReturnValue(testKey);

      await credentialsManager.initialize('test-password');

      expect(mockCryptoUtils.decryptMasterKey).toHaveBeenCalledWith(encryptedMasterKey, 'test-password');
    });

    it('should load existing plain key file', async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('plain-key-content');
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);

      await credentialsManager.initialize();

      expect(mockFileUtils.readBinaryFile).toHaveBeenCalledWith(keyFilePath);
    });

    it('should throw error when password required but not provided', async () => {
      const encryptedMasterKey = {
        salt: 'test-salt',
        iv: 'test-iv',
        encryptedKey: 'encrypted-key-data',
        encrypted: true
      };
      
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue(JSON.stringify(encryptedMasterKey));

      await expect(credentialsManager.initialize()).rejects.toThrow(CryptoError);
    });

    it('should throw error when invalid password provided', async () => {
      const encryptedMasterKey = {
        salt: 'test-salt',
        iv: 'test-iv',
        encryptedKey: 'encrypted-key-data',
        encrypted: true
      };
      
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue(JSON.stringify(encryptedMasterKey));
      mockCryptoUtils.decryptMasterKey.mockImplementation(() => {
        throw new Error('Invalid password');
      });

      await expect(credentialsManager.initialize('wrong-password')).rejects.toThrow(CryptoError);
    });

    it('should load existing credentials after initialization', async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      const existingCredentials = {
        credentials: [
          {
            name: 'Test Confluence',
            baseUrl: 'https://test.atlassian.net',
            token: 'test-token',
            spaceKey: 'TEST',
            puppeteerLogin: false
          }
        ],
        version: '2.0'
      };
      
      mockFileUtils.fileExists.mockImplementation((path) => {
        if (path === keyFilePath) return Promise.resolve(true);
        if (path === credentialsFilePath) return Promise.resolve(true);
        return Promise.resolve(false);
      });
      mockFileUtils.readFile.mockImplementation((path) => {
        if (path === keyFilePath) return Promise.resolve('plain-key-content');
        if (path === credentialsFilePath) return Promise.resolve(JSON.stringify({
          salt: 'test-salt',
          iv: 'test-iv',
          encryptedData: 'encrypted-credentials',
          encrypted: true,
          version: '2.0'
        }));
        return Promise.resolve('');
      });
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);
      mockCryptoUtils.decryptData.mockReturnValue(JSON.stringify(existingCredentials));

      await credentialsManager.initialize();

      const credentials = credentialsManager.getCredentials();
      expect(credentials).toHaveLength(1);
      expect(credentials[0].name).toBe('Test Confluence');
    });
  });

  describe('addOrUpdateCredential', () => {
    beforeEach(async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(false);
      mockFileUtils.writeJsonFile.mockResolvedValue();
      
      await credentialsManager.initialize('test-password');
    });

    it('should add new credential', async () => {
      const credential: ConfluenceCredential = {
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        token: 'test-token',
        spaceKey: 'TEST',
        puppeteerLogin: false
      };

      await credentialsManager.addOrUpdateCredential(credential);

      const credentials = credentialsManager.getCredentials();
      expect(credentials).toHaveLength(1);
      expect(credentials[0]).toEqual(credential);
    });

    it('should update existing credential', async () => {
      const originalCredential: ConfluenceCredential = {
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        token: 'old-token',
        spaceKey: 'TEST',
        puppeteerLogin: false
      };

      const updatedCredential: ConfluenceCredential = {
        name: 'Test Confluence Updated',
        baseUrl: 'https://test.atlassian.net',
        token: 'new-token',
        spaceKey: 'TEST',
        puppeteerLogin: true
      };

      await credentialsManager.addOrUpdateCredential(originalCredential);
      await credentialsManager.addOrUpdateCredential(updatedCredential);

      const credentials = credentialsManager.getCredentials();
      expect(credentials).toHaveLength(1);
      expect(credentials[0].token).toBe('new-token');
      expect(credentials[0].puppeteerLogin).toBe(true);
    });

    it('should save credentials after adding', async () => {
      const credential: ConfluenceCredential = {
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        token: 'test-token',
        spaceKey: 'TEST',
        puppeteerLogin: false
      };

      await credentialsManager.addOrUpdateCredential(credential);

      expect(mockCryptoUtils.encryptData).toHaveBeenCalled();
      expect(mockFileUtils.writeJsonFile).toHaveBeenCalledWith(
        credentialsFilePath,
        expect.objectContaining({
          encrypted: true,
          version: '2.0'
        }),
        { mode: 0o600 }
      );
    });
  });

  describe('getCredentialByBaseUrl', () => {
    beforeEach(async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(false);
      mockFileUtils.writeJsonFile.mockResolvedValue();
      
      await credentialsManager.initialize('test-password');
    });

    it('should return credential for matching baseUrl', async () => {
      const credential: ConfluenceCredential = {
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        token: 'test-token',
        spaceKey: 'TEST',
        puppeteerLogin: false
      };

      await credentialsManager.addOrUpdateCredential(credential);
      
      const found = credentialsManager.getCredentialByBaseUrl('https://test.atlassian.net');
      expect(found).toEqual(credential);
    });

    it('should return undefined for non-matching baseUrl', async () => {
      const credential: ConfluenceCredential = {
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        token: 'test-token',
        spaceKey: 'TEST',
        puppeteerLogin: false
      };

      await credentialsManager.addOrUpdateCredential(credential);
      
      const found = credentialsManager.getCredentialByBaseUrl('https://other.atlassian.net');
      expect(found).toBeUndefined();
    });
  });

  describe('removeCredential', () => {
    beforeEach(async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(false);
      mockFileUtils.writeJsonFile.mockResolvedValue();
      
      await credentialsManager.initialize('test-password');
    });

    it('should remove credential by baseUrl', async () => {
      const credential1: ConfluenceCredential = {
        name: 'Test Confluence 1',
        baseUrl: 'https://test1.atlassian.net',
        token: 'test-token-1',
        spaceKey: 'TEST1',
        puppeteerLogin: false
      };

      const credential2: ConfluenceCredential = {
        name: 'Test Confluence 2',
        baseUrl: 'https://test2.atlassian.net',
        token: 'test-token-2',
        spaceKey: 'TEST2',
        puppeteerLogin: false
      };

      await credentialsManager.addOrUpdateCredential(credential1);
      await credentialsManager.addOrUpdateCredential(credential2);
      
      expect(credentialsManager.getCredentials()).toHaveLength(2);

      await credentialsManager.removeCredential('https://test1.atlassian.net');
      
      const remainingCredentials = credentialsManager.getCredentials();
      expect(remainingCredentials).toHaveLength(1);
      expect(remainingCredentials[0].baseUrl).toBe('https://test2.atlassian.net');
    });

    it('should return true when credential was removed', async () => {
      const credential: ConfluenceCredential = {
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        token: 'test-token',
        spaceKey: 'TEST',
        puppeteerLogin: false
      };

      await credentialsManager.addOrUpdateCredential(credential);
      
      const removed = await credentialsManager.removeCredential('https://test.atlassian.net');
      expect(removed).toBe(true);
    });

    it('should return false when credential was not found', async () => {
      const removed = await credentialsManager.removeCredential('https://nonexistent.atlassian.net');
      expect(removed).toBe(false);
    });
  });

  describe('exportCredentials', () => {
    beforeEach(async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(false);
      mockFileUtils.writeJsonFile.mockResolvedValue();
      
      await credentialsManager.initialize('test-password');
    });

    it('should export credentials to file', async () => {
      const credential: ConfluenceCredential = {
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        token: 'test-token',
        spaceKey: 'TEST',
        puppeteerLogin: false
      };

      await credentialsManager.addOrUpdateCredential(credential);
      
      const exportPath = path.join(testDir, 'export.json');
      await credentialsManager.exportCredentials(exportPath);

      expect(mockFileUtils.writeJsonFile).toHaveBeenCalledWith(
        exportPath,
        expect.objectContaining({
          credentials: [credential],
          version: '2.0'
        }),
        { mode: 0o600 }
      );
    });

    it('should return number of exported credentials', async () => {
      const credential1: ConfluenceCredential = {
        name: 'Test Confluence 1',
        baseUrl: 'https://test1.atlassian.net',
        token: 'test-token-1',
        spaceKey: 'TEST1',
        puppeteerLogin: false
      };

      const credential2: ConfluenceCredential = {
        name: 'Test Confluence 2',
        baseUrl: 'https://test2.atlassian.net',
        token: 'test-token-2',
        spaceKey: 'TEST2',
        puppeteerLogin: false
      };

      await credentialsManager.addOrUpdateCredential(credential1);
      await credentialsManager.addOrUpdateCredential(credential2);
      
      const exportPath = path.join(testDir, 'export.json');
      const count = await credentialsManager.exportCredentials(exportPath);
      
      expect(count).toBe(2);
    });
  });

  describe('importCredentials', () => {
    beforeEach(async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(false);
      mockFileUtils.writeJsonFile.mockResolvedValue();
      
      await credentialsManager.initialize('test-password');
    });

    it('should import credentials from file', async () => {
      const importData = {
        credentials: [
          {
            name: 'Imported Confluence',
            baseUrl: 'https://imported.atlassian.net',
            token: 'imported-token',
            spaceKey: 'IMPORT',
            puppeteerLogin: false
          }
        ],
        version: '2.0'
      };

      const importPath = path.join(testDir, 'import.json');
      mockFileUtils.readJsonFile.mockResolvedValue(importData);

      const count = await credentialsManager.importCredentials(importPath);

      expect(count).toBe(1);
      
      const credentials = credentialsManager.getCredentials();
      expect(credentials).toHaveLength(1);
      expect(credentials[0].name).toBe('Imported Confluence');
    });

    it('should validate imported credentials format', async () => {
      const invalidData = {
        credentials: 'not-an-array',
        version: '2.0'
      };

      const importPath = path.join(testDir, 'import.json');
      mockFileUtils.readJsonFile.mockResolvedValue(invalidData);

      await expect(credentialsManager.importCredentials(importPath)).rejects.toThrow(ValidationError);
    });

    it('should validate individual credential objects', async () => {
      const invalidData = {
        credentials: [
          {
            name: 'Invalid Credential',
            // Missing required fields
          }
        ],
        version: '2.0'
      };

      const importPath = path.join(testDir, 'import.json');
      mockFileUtils.readJsonFile.mockResolvedValue(invalidData);

      await expect(credentialsManager.importCredentials(importPath)).rejects.toThrow(ValidationError);
    });

    it('should merge with existing credentials', async () => {
      // Add existing credential
      const existingCredential: ConfluenceCredential = {
        name: 'Existing Confluence',
        baseUrl: 'https://existing.atlassian.net',
        token: 'existing-token',
        spaceKey: 'EXIST',
        puppeteerLogin: false
      };

      await credentialsManager.addOrUpdateCredential(existingCredential);

      // Import new credential
      const importData = {
        credentials: [
          {
            name: 'Imported Confluence',
            baseUrl: 'https://imported.atlassian.net',
            token: 'imported-token',
            spaceKey: 'IMPORT',
            puppeteerLogin: false
          }
        ],
        version: '2.0'
      };

      const importPath = path.join(testDir, 'import.json');
      mockFileUtils.readJsonFile.mockResolvedValue(importData);

      await credentialsManager.importCredentials(importPath);

      const credentials = credentialsManager.getCredentials();
      expect(credentials).toHaveLength(2);
    });
  });

  describe('cleanup', () => {
    it('should cleanup sensitive data', async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(false);
      mockFileUtils.writeJsonFile.mockResolvedValue();
      
      await credentialsManager.initialize('test-password');

      await credentialsManager.cleanup();

      expect(mockCryptoUtils.secureWipe).toHaveBeenCalled();
      expect(credentialsManager.getCredentials()).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle file read errors gracefully', async () => {
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockRejectedValue(new Error('File read error'));

      await expect(credentialsManager.initialize()).rejects.toThrow();
    });

    it('should handle encryption errors gracefully', async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(false);
      mockFileUtils.writeJsonFile.mockResolvedValue();
      mockCryptoUtils.encryptData.mockImplementation(() => {
        throw new Error('Encryption failed');
      });
      
      await credentialsManager.initialize('test-password');

      const credential: ConfluenceCredential = {
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        token: 'test-token',
        spaceKey: 'TEST',
        puppeteerLogin: false
      };

      await expect(credentialsManager.addOrUpdateCredential(credential)).rejects.toThrow();
    });

    it('should handle invalid credentials file format', async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockImplementation((path) => {
        if (path === keyFilePath) return Promise.resolve(false);
        if (path === credentialsFilePath) return Promise.resolve(true);
        return Promise.resolve(false);
      });
      mockFileUtils.writeJsonFile.mockResolvedValue();
      mockFileUtils.readFile.mockImplementation((path) => {
        if (path === credentialsFilePath) return Promise.resolve('invalid json');
        return Promise.resolve('');
      });

      await credentialsManager.initialize('test-password');

      // Should start with empty credentials when file is invalid
      expect(credentialsManager.getCredentials()).toHaveLength(0);
    });
  });
});
