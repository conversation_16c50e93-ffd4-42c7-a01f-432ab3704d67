/**
 * Tests for SecureCookieManager
 */

import { SecureCookieManager, EnhancedCookie, CookieStorage } from '../../src/services/secure-cookie-manager';
import { CryptoUtils } from '../../src/utils/crypto-utils';
import { FileUtils } from '../../src/utils/file-utils';
import { CryptoError } from '../../src/errors';
import { defaultConfig } from '../../src/config/app-config';
import fs from 'fs/promises';
import path from 'path';

// Mock dependencies
jest.mock('../../src/utils/crypto-utils');
jest.mock('../../src/utils/file-utils');
jest.mock('../../src/utils/logger');

const mockCryptoUtils = CryptoUtils as jest.Mocked<typeof CryptoUtils>;
const mockFileUtils = FileUtils as jest.Mocked<typeof FileUtils>;

describe('SecureCookieManager', () => {
  let cookieManager: SecureCookieManager;
  let testDir: string;
  let keyFilePath: string;
  let cookieFilePath: string;

  beforeEach(async () => {
    // Create temporary test directory
    testDir = path.join(__dirname, 'temp-cookie-test');
    await fs.mkdir(testDir, { recursive: true });
    
    keyFilePath = path.join(testDir, 'master.key');
    cookieFilePath = path.join(testDir, 'cookies.json');

    // Mock config paths
    jest.spyOn(defaultConfig.paths, 'keyFile', 'get').mockReturnValue(keyFilePath);
    jest.spyOn(defaultConfig.paths, 'cookiesFile', 'get').mockReturnValue(cookieFilePath);
    jest.spyOn(defaultConfig.paths, 'configDir', 'get').mockReturnValue(testDir);

    // Reset mocks
    jest.clearAllMocks();

    // Setup default mock implementations
    mockFileUtils.ensureDirectory.mockResolvedValue();
    mockFileUtils.fileExists.mockResolvedValue(false);
    mockCryptoUtils.generateKey.mockReturnValue(Buffer.from('test-key-32-bytes-long-for-aes256'));
    mockCryptoUtils.encryptData.mockReturnValue({
      salt: 'test-salt',
      iv: 'test-iv',
      encryptedData: 'test-encrypted-data'
    });
    mockCryptoUtils.decryptData.mockReturnValue('{"cookies":[],"version":"2.0"}');
    mockCryptoUtils.secureWipe.mockImplementation(() => {});

    cookieManager = new SecureCookieManager();
  });

  afterEach(async () => {
    // Cleanup
    try {
      await cookieManager.cleanup();
      await fs.rm(testDir, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Constructor', () => {
    it('should create instance with correct file paths', () => {
      expect(cookieManager).toBeInstanceOf(SecureCookieManager);
    });
  });

  describe('initialize', () => {
    it('should initialize with existing plain key file', async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('plain-key-content');
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);

      await cookieManager.initialize();

      expect(mockFileUtils.ensureDirectory).toHaveBeenCalledWith(testDir);
      expect(mockFileUtils.fileExists).toHaveBeenCalledWith(keyFilePath);
      expect(mockFileUtils.readBinaryFile).toHaveBeenCalledWith(keyFilePath);
    });

    it('should initialize with existing encrypted key file', async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      const encryptedMasterKey = {
        salt: 'test-salt',
        iv: 'test-iv',
        encryptedKey: 'encrypted-key-data',
        encrypted: true
      };
      
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue(JSON.stringify(encryptedMasterKey));
      mockCryptoUtils.decryptMasterKey.mockReturnValue(testKey);

      await cookieManager.initialize('test-password');

      expect(mockCryptoUtils.decryptMasterKey).toHaveBeenCalledWith(encryptedMasterKey, 'test-password');
    });

    it('should throw error when key file not found', async () => {
      mockFileUtils.fileExists.mockResolvedValue(false);

      await expect(cookieManager.initialize()).rejects.toThrow(CryptoError);
    });

    it('should throw error when password required but not provided', async () => {
      const encryptedMasterKey = {
        salt: 'test-salt',
        iv: 'test-iv',
        encryptedKey: 'encrypted-key-data',
        encrypted: true
      };
      
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue(JSON.stringify(encryptedMasterKey));

      await expect(cookieManager.initialize()).rejects.toThrow(CryptoError);
    });

    it('should throw error when invalid password provided', async () => {
      const encryptedMasterKey = {
        salt: 'test-salt',
        iv: 'test-iv',
        encryptedKey: 'encrypted-key-data',
        encrypted: true
      };
      
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue(JSON.stringify(encryptedMasterKey));
      mockCryptoUtils.decryptMasterKey.mockImplementation(() => {
        throw new Error('Invalid password');
      });

      await expect(cookieManager.initialize('wrong-password')).rejects.toThrow(CryptoError);
    });

    it('should load existing cookies after initialization', async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      const existingCookies = {
        cookies: [
          {
            name: 'test-cookie',
            value: 'test-value',
            domain: 'example.com',
            baseUrl: 'https://example.com',
            savedAt: Date.now()
          }
        ],
        version: '2.0'
      };
      
      mockFileUtils.fileExists.mockImplementation((path) => {
        if (path === keyFilePath) return Promise.resolve(true);
        if (path === cookieFilePath) return Promise.resolve(true);
        return Promise.resolve(false);
      });
      mockFileUtils.readFile.mockImplementation((path) => {
        if (path === keyFilePath) return Promise.resolve('plain-key-content');
        if (path === cookieFilePath) return Promise.resolve(JSON.stringify({
          salt: 'test-salt',
          iv: 'test-iv',
          encryptedData: 'encrypted-cookies',
          encrypted: true,
          version: '2.0'
        }));
        return Promise.resolve('');
      });
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);
      mockCryptoUtils.decryptData.mockReturnValue(JSON.stringify(existingCookies));

      await cookieManager.initialize();

      const cookies = cookieManager.getCookies();
      expect(cookies).toHaveLength(1);
      expect(cookies[0].name).toBe('test-cookie');
    });
  });

  describe('saveCookies', () => {
    beforeEach(async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('plain-key-content');
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);
      mockFileUtils.writeJsonFile.mockResolvedValue();
      
      await cookieManager.initialize();
    });

    it('should save cookies with encryption', async () => {
      const testCookies = [
        {
          name: 'session',
          value: 'abc123',
          domain: 'example.com',
          path: '/',
          httpOnly: true,
          secure: true
        }
      ];

      await cookieManager.saveCookies(testCookies, 'https://example.com');

      expect(mockCryptoUtils.encryptData).toHaveBeenCalled();
      expect(mockFileUtils.writeJsonFile).toHaveBeenCalledWith(
        cookieFilePath,
        expect.objectContaining({
          encrypted: true,
          version: '2.0'
        }),
        { mode: 0o600 }
      );
    });

    it('should filter cookies by domain', async () => {
      const testCookies = [
        {
          name: 'session',
          value: 'abc123',
          domain: 'example.com',
          path: '/'
        },
        {
          name: 'other',
          value: 'xyz789',
          domain: 'other.com',
          path: '/'
        }
      ];

      await cookieManager.saveCookies(testCookies, 'https://example.com');

      const savedCookies = cookieManager.getCookies();
      expect(savedCookies).toHaveLength(1);
      expect(savedCookies[0].domain).toBe('example.com');
    });

    it('should replace existing cookies for same baseUrl', async () => {
      const initialCookies = [
        {
          name: 'old-session',
          value: 'old-value',
          domain: 'example.com',
          path: '/'
        }
      ];

      const newCookies = [
        {
          name: 'new-session',
          value: 'new-value',
          domain: 'example.com',
          path: '/'
        }
      ];

      await cookieManager.saveCookies(initialCookies, 'https://example.com');
      await cookieManager.saveCookies(newCookies, 'https://example.com');

      const savedCookies = cookieManager.getCookies();
      expect(savedCookies).toHaveLength(1);
      expect(savedCookies[0].name).toBe('new-session');
    });
  });

  describe('getCookiesForBaseUrl', () => {
    beforeEach(async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('plain-key-content');
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);
      
      await cookieManager.initialize();
    });

    it('should return cookies for matching baseUrl', async () => {
      const testCookies = [
        {
          name: 'session',
          value: 'abc123',
          domain: 'example.com',
          path: '/'
        }
      ];

      await cookieManager.saveCookies(testCookies, 'https://example.com');
      
      const cookies = cookieManager.getCookiesForBaseUrl('https://example.com');
      expect(cookies).toHaveLength(1);
      expect(cookies[0].name).toBe('session');
    });

    it('should return empty array for non-matching baseUrl', async () => {
      const testCookies = [
        {
          name: 'session',
          value: 'abc123',
          domain: 'example.com',
          path: '/'
        }
      ];

      await cookieManager.saveCookies(testCookies, 'https://example.com');
      
      const cookies = cookieManager.getCookiesForBaseUrl('https://other.com');
      expect(cookies).toHaveLength(0);
    });

    it('should handle subdomain matching', async () => {
      const testCookies = [
        {
          name: 'session',
          value: 'abc123',
          domain: '.example.com',
          path: '/'
        }
      ];

      await cookieManager.saveCookies(testCookies, 'https://sub.example.com');
      
      const cookies = cookieManager.getCookiesForBaseUrl('https://sub.example.com');
      expect(cookies).toHaveLength(1);
    });

    it('should handle invalid URLs gracefully', () => {
      const cookies = cookieManager.getCookiesForBaseUrl('invalid-url');
      expect(cookies).toHaveLength(0);
    });
  });

  describe('clearCookies', () => {
    beforeEach(async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('plain-key-content');
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);
      mockFileUtils.writeJsonFile.mockResolvedValue();
      
      await cookieManager.initialize();
    });

    it('should clear all cookies', async () => {
      const testCookies = [
        {
          name: 'session',
          value: 'abc123',
          domain: 'example.com',
          path: '/'
        }
      ];

      await cookieManager.saveCookies(testCookies, 'https://example.com');
      expect(cookieManager.getCookies()).toHaveLength(1);

      await cookieManager.clearCookies();
      expect(cookieManager.getCookies()).toHaveLength(0);
    });

    it('should save empty cookies to file', async () => {
      await cookieManager.clearCookies();

      expect(mockFileUtils.writeJsonFile).toHaveBeenCalledWith(
        cookieFilePath,
        expect.objectContaining({
          encrypted: true,
          version: '2.0'
        }),
        { mode: 0o600 }
      );
    });
  });

  describe('clearCookiesForBaseUrl', () => {
    beforeEach(async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('plain-key-content');
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);
      mockFileUtils.writeJsonFile.mockResolvedValue();
      
      await cookieManager.initialize();
    });

    it('should clear cookies for specific baseUrl', async () => {
      const cookies1 = [{ name: 'session1', value: 'abc', domain: 'example.com', path: '/' }];
      const cookies2 = [{ name: 'session2', value: 'xyz', domain: 'other.com', path: '/' }];

      await cookieManager.saveCookies(cookies1, 'https://example.com');
      await cookieManager.saveCookies(cookies2, 'https://other.com');
      
      expect(cookieManager.getCookies()).toHaveLength(2);

      await cookieManager.clearCookiesForBaseUrl('https://example.com');
      
      const remainingCookies = cookieManager.getCookies();
      expect(remainingCookies).toHaveLength(1);
      expect(remainingCookies[0].name).toBe('session2');
    });

    it('should not save if no cookies were cleared', async () => {
      mockFileUtils.writeJsonFile.mockClear();

      await cookieManager.clearCookiesForBaseUrl('https://nonexistent.com');

      expect(mockFileUtils.writeJsonFile).not.toHaveBeenCalled();
    });
  });

  describe('hasCookiesForBaseUrl', () => {
    beforeEach(async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('plain-key-content');
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);
      
      await cookieManager.initialize();
    });

    it('should return true when cookies exist for baseUrl', async () => {
      const testCookies = [
        {
          name: 'session',
          value: 'abc123',
          domain: 'example.com',
          path: '/'
        }
      ];

      await cookieManager.saveCookies(testCookies, 'https://example.com');
      
      expect(cookieManager.hasCookiesForBaseUrl('https://example.com')).toBe(true);
    });

    it('should return false when no cookies exist for baseUrl', () => {
      expect(cookieManager.hasCookiesForBaseUrl('https://example.com')).toBe(false);
    });
  });

  describe('getCookieStats', () => {
    beforeEach(async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('plain-key-content');
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);
      
      await cookieManager.initialize();
    });

    it('should return correct statistics', async () => {
      const testCookies = [
        { name: 'session1', value: 'abc', domain: 'example.com', path: '/' },
        { name: 'session2', value: 'xyz', domain: 'example.com', path: '/' },
        { name: 'session3', value: '123', domain: 'other.com', path: '/' }
      ];

      await cookieManager.saveCookies(testCookies.slice(0, 2), 'https://example.com');
      await cookieManager.saveCookies(testCookies.slice(2), 'https://other.com');

      const stats = cookieManager.getCookieStats();

      expect(stats.total).toBe(3);
      expect(stats.byDomain['example.com']).toBe(2);
      expect(stats.byDomain['other.com']).toBe(1);
      expect(stats.encrypted).toBe(true);
    });

    it('should handle empty cookies', () => {
      const stats = cookieManager.getCookieStats();

      expect(stats.total).toBe(0);
      expect(stats.byDomain).toEqual({});
      expect(stats.encrypted).toBe(true);
    });
  });

  describe('cleanup', () => {
    it('should cleanup sensitive data', async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('plain-key-content');
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);
      
      await cookieManager.initialize();

      await cookieManager.cleanup();

      expect(mockCryptoUtils.secureWipe).toHaveBeenCalled();
      expect(cookieManager.getCookies()).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle file read errors gracefully', async () => {
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockRejectedValue(new Error('File read error'));

      await expect(cookieManager.initialize()).rejects.toThrow();
    });

    it('should handle encryption errors gracefully', async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('plain-key-content');
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);
      mockCryptoUtils.encryptData.mockImplementation(() => {
        throw new Error('Encryption failed');
      });
      
      await cookieManager.initialize();

      await expect(cookieManager.saveCookies([], 'https://example.com')).rejects.toThrow();
    });

    it('should handle invalid cookie file format', async () => {
      const testKey = Buffer.from('test-key-32-bytes-long-for-aes256');
      mockFileUtils.fileExists.mockImplementation((path) => {
        if (path === keyFilePath) return Promise.resolve(true);
        if (path === cookieFilePath) return Promise.resolve(true);
        return Promise.resolve(false);
      });
      mockFileUtils.readFile.mockImplementation((path) => {
        if (path === keyFilePath) return Promise.resolve('plain-key-content');
        if (path === cookieFilePath) return Promise.resolve('invalid json');
        return Promise.resolve('');
      });
      mockFileUtils.readBinaryFile.mockResolvedValue(testKey);

      await cookieManager.initialize();

      // Should start with empty cookies when file is invalid
      expect(cookieManager.getCookies()).toHaveLength(0);
    });
  });
});
