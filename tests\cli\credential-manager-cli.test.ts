/**
 * Tests for Credential Manager CLI
 */

import * as readline from 'readline';
import * as fs from 'fs/promises';
import { SecureCredentialsManager } from '../../src/services/secure-credentials-manager';
import { askPassword } from '../../src/utils/password-input';
import { logger } from '../../src/utils/logger';

// Mock dependencies
jest.mock('readline');
jest.mock('fs/promises');
jest.mock('../../src/services/secure-credentials-manager');
jest.mock('../../src/utils/password-input');
jest.mock('../../src/utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

// Mock console methods
const mockConsoleLog = jest.spyOn(console, 'log').mockImplementation();
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation();

// Mock process methods
const mockProcessExit = jest.spyOn(process, 'exit').mockImplementation();

// Mock process.argv
const originalArgv = process.argv;

describe('Credential Manager CLI', () => {
  let mockManager: jest.Mocked<SecureCredentialsManager>;
  let mockRl: jest.Mocked<readline.Interface>;
  let mockAskPassword: jest.MockedFunction<typeof askPassword>;
  let mockFs: jest.Mocked<typeof fs>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset process.argv
    process.argv = ['node', 'credential-manager-cli.js'];

    // Mock SecureCredentialsManager
    mockManager = {
      initialize: jest.fn(),
      cleanup: jest.fn(),
      getCredentials: jest.fn(),
      addOrUpdateCredential: jest.fn(),
      removeCredential: jest.fn(),
      getCredentialByBaseUrl: jest.fn()
    } as any;

    (SecureCredentialsManager as jest.Mock).mockImplementation(() => mockManager);

    // Mock readline
    mockRl = {
      question: jest.fn((query: string, callback: (answer: string) => void) => {
        // Default behavior - can be overridden in tests
        callback('');
        return mockRl;
      }),
      close: jest.fn()
    } as any;

    (readline.createInterface as jest.Mock).mockReturnValue(mockRl);

    // Mock askPassword
    mockAskPassword = askPassword as jest.MockedFunction<typeof askPassword>;

    // Mock fs
    mockFs = fs as jest.Mocked<typeof fs>;
  });

  afterEach(() => {
    process.argv = originalArgv;
  });

  afterAll(() => {
    mockConsoleLog.mockRestore();
    mockConsoleError.mockRestore();
    mockProcessExit.mockRestore();
  });

  describe('Command Line Arguments', () => {
    it('should show help when --help flag is provided', async () => {
      process.argv = ['node', 'credential-manager-cli.js', '--help'];

      // Import and run main function
      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('Usage: credential-manager-cli [options]');
      expect(mockConsoleLog).toHaveBeenCalledWith('Options:');
      expect(mockConsoleLog).toHaveBeenCalledWith('  --import, -i <file>  Import credentials from specified JSON file');
      expect(mockConsoleLog).toHaveBeenCalledWith('  --help, -h           Show this help message');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });

    it('should show help when -h flag is provided', async () => {
      process.argv = ['node', 'credential-manager-cli.js', '-h'];

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('Usage: credential-manager-cli [options]');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });

    it('should handle import flag with file path', async () => {
      process.argv = ['node', 'credential-manager-cli.js', '--import', 'test.json'];

      mockAskPassword.mockResolvedValue('password123');
      mockManager.initialize.mockResolvedValue();
      mockFs.readFile.mockResolvedValue('{"credentials": []}');

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('Importing from: test.json');
      expect(mockManager.cleanup).toHaveBeenCalled();
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });

    it('should handle -i flag with file path', async () => {
      process.argv = ['node', 'credential-manager-cli.js', '-i', 'config.json'];

      mockAskPassword.mockResolvedValue('password123');
      mockManager.initialize.mockResolvedValue();
      mockFs.readFile.mockResolvedValue('{"credentials": []}');

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('Importing from: config.json');
    });

    it('should show error when import flag is missing file path', async () => {
      process.argv = ['node', 'credential-manager-cli.js', '--import'];

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('Error: --import flag requires a file path argument.');
      expect(mockConsoleError).toHaveBeenCalledWith('Usage: credential-manager-cli --import <file-path>');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });
  });

  describe('Password Authentication', () => {
    it('should initialize manager with correct password', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      mockAskPassword.mockResolvedValue('correct-password');
      mockManager.initialize.mockResolvedValue();

      // Mock the interactive loop to exit immediately
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callback('6'); // Exit option
        }
        return mockRl;
      });

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockAskPassword).toHaveBeenCalledWith('Enter master password: ');
      expect(mockManager.initialize).toHaveBeenCalledWith('correct-password');
    });

    it('should retry on incorrect password', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      mockAskPassword
        .mockResolvedValueOnce('wrong-password')
        .mockResolvedValueOnce('correct-password');

      mockManager.initialize
        .mockRejectedValueOnce(new Error('Invalid password'))
        .mockResolvedValueOnce();

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callback('6'); // Exit option
        }
        return mockRl;
      });

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockAskPassword).toHaveBeenCalledTimes(2);
      expect(mockAskPassword).toHaveBeenNthCalledWith(2, 'Enter master password (attempt 2/3): ');
      expect(mockConsoleError).toHaveBeenCalledWith('\nFailed to initialize credentials manager: Invalid password');
    });

    it('should exit after maximum password attempts', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      mockAskPassword.mockResolvedValue('wrong-password');
      mockManager.initialize.mockRejectedValue(new Error('Invalid password'));

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockAskPassword).toHaveBeenCalledTimes(3);
      expect(mockConsoleError).toHaveBeenCalledWith('\nMaximum attempts (3) reached. Exiting...');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });

    it('should handle user cancellation (Ctrl+C)', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      mockAskPassword.mockResolvedValue(''); // Empty password indicates cancellation

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('\nOperation cancelled by user.');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });
  });

  describe('Interactive Menu', () => {
    beforeEach(() => {
      mockAskPassword.mockResolvedValue('password123');
      mockManager.initialize.mockResolvedValue();
    });

    it('should show main menu options', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callback('6'); // Exit option
        }
        return mockRl;
      });

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('\nOptions:');
      expect(mockConsoleLog).toHaveBeenCalledWith('1. List all credentials');
      expect(mockConsoleLog).toHaveBeenCalledWith('2. Add/Update credential');
      expect(mockConsoleLog).toHaveBeenCalledWith('3. Remove credential');
      expect(mockConsoleLog).toHaveBeenCalledWith('4. Import from JSON file');
      expect(mockConsoleLog).toHaveBeenCalledWith('5. Export credentials to JSON');
      expect(mockConsoleLog).toHaveBeenCalledWith('6. Exit');
    });

    it('should handle list credentials option', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const mockCredentials = [
        {
          name: 'Test Instance',
          baseUrl: 'https://test.atlassian.net',
          spaceKey: 'TEST',
          username: 'testuser',
          token: 'token123',
          puppeteerLogin: false
        }
      ];

      mockManager.getCredentials.mockReturnValue(mockCredentials);

      let callCount = 0;
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('1'); // List credentials
          } else {
            callback('6'); // Exit
          }
        }
        return mockRl;
      });

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockManager.getCredentials).toHaveBeenCalled();
      expect(mockConsoleLog).toHaveBeenCalledWith('\nStored credentials:');
    });

    it('should handle empty credentials list', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      mockManager.getCredentials.mockReturnValue([]);

      let callCount = 0;
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('1'); // List credentials
          } else {
            callback('6'); // Exit
          }
        }
        return mockRl;
      });

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('No credentials stored.');
    });

    it('should handle invalid menu option', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      let callCount = 0;
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('9'); // Invalid option
          } else {
            callback('6'); // Exit
          }
        }
        return mockRl;
      });

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('Invalid option. Please try again.');
    });

    it('should handle exit option', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callback('6'); // Exit option
        }
        return mockRl;
      });

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('Exiting...');
      expect(mockManager.cleanup).toHaveBeenCalled();
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });
  });

  describe('Add/Update Credential', () => {
    beforeEach(() => {
      mockAskPassword.mockResolvedValue('password123');
      mockManager.initialize.mockResolvedValue();
    });

    it('should add new credential with API token', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const responses = [
        'Test Instance',           // name
        'https://test.atlassian.net', // baseUrl
        'TEST',                   // spaceKey
        'n',                      // puppeteerLogin
        'testuser',               // username
        'token123'                // token
      ];

      let responseIndex = 0;
      let callCount = 0;

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('2'); // Add/Update credential
          } else {
            callback('6'); // Exit
          }
        } else if (responseIndex < responses.length) {
          callback(responses[responseIndex++]);
        }
        return mockRl;
      });

      mockManager.getCredentialByName.mockReturnValue(undefined);
      mockManager.addCredential.mockResolvedValue();

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockManager.addCredential).toHaveBeenCalledWith(expect.objectContaining({
        name: 'Test Instance',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        token: 'token123',
        puppeteerLogin: false
      }));
    });

    it('should add new credential with browser login', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const responses = [
        'Test Instance',           // name
        'https://test.atlassian.net', // baseUrl
        'TEST',                   // spaceKey
        'y',                      // puppeteerLogin
        'testuser',               // username
      ];

      let responseIndex = 0;
      let callCount = 0;

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('2'); // Add/Update credential
          } else {
            callback('6'); // Exit
          }
        } else if (responseIndex < responses.length) {
          callback(responses[responseIndex++]);
        }
        return mockRl;
      });

      mockAskPassword.mockResolvedValueOnce('password123').mockResolvedValueOnce('userpass123');
      mockManager.getCredentialByName.mockReturnValue(undefined);
      mockManager.addCredential.mockResolvedValue();

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockManager.addCredential).toHaveBeenCalledWith(expect.objectContaining({
        name: 'Test Instance',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        password: 'userpass123',
        puppeteerLogin: true
      }));
    });

    it('should update existing credential', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const existingCredential = {
        name: 'Test Instance',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'olduser',
        token: 'oldtoken',
        puppeteerLogin: false
      };

      const responses = [
        'Test Instance',           // name
        'y',                      // confirm update
        'https://test.atlassian.net', // baseUrl
        'TEST',                   // spaceKey
        'n',                      // puppeteerLogin
        'newuser',                // username
        'newtoken'                // token
      ];

      let responseIndex = 0;
      let callCount = 0;

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('2'); // Add/Update credential
          } else {
            callback('6'); // Exit
          }
        } else if (responseIndex < responses.length) {
          callback(responses[responseIndex++]);
        }
        return mockRl;
      });

      mockManager.getCredentialByName.mockReturnValue(existingCredential);
      mockManager.updateCredential.mockResolvedValue();

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('Credential "Test Instance" already exists.');
      expect(mockManager.updateCredential).toHaveBeenCalledWith('Test Instance', expect.objectContaining({
        name: 'Test Instance',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'newuser',
        token: 'newtoken',
        puppeteerLogin: false
      }));
    });

    it('should cancel update when user declines', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const existingCredential = {
        name: 'Test Instance',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'olduser',
        token: 'oldtoken',
        puppeteerLogin: false
      };

      const responses = [
        'Test Instance',           // name
        'n'                       // decline update
      ];

      let responseIndex = 0;
      let callCount = 0;

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('2'); // Add/Update credential
          } else {
            callback('6'); // Exit
          }
        } else if (responseIndex < responses.length) {
          callback(responses[responseIndex++]);
        }
        return mockRl;
      });

      mockManager.getCredentialByName.mockReturnValue(existingCredential);

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('Update cancelled.');
      expect(mockManager.updateCredential).not.toHaveBeenCalled();
    });
  });

  describe('Remove Credential', () => {
    beforeEach(() => {
      mockAskPassword.mockResolvedValue('password123');
      mockManager.initialize.mockResolvedValue();
    });

    it('should remove existing credential', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const existingCredential = {
        name: 'Test Instance',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        token: 'token123',
        puppeteerLogin: false
      };

      const responses = [
        'Test Instance',           // name
        'y'                       // confirm removal
      ];

      let responseIndex = 0;
      let callCount = 0;

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('3'); // Remove credential
          } else {
            callback('6'); // Exit
          }
        } else if (responseIndex < responses.length) {
          callback(responses[responseIndex++]);
        }
        return mockRl;
      });

      mockManager.getCredentialByName.mockReturnValue(existingCredential);
      mockManager.removeCredential.mockResolvedValue();

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockManager.removeCredential).toHaveBeenCalledWith('Test Instance');
      expect(mockConsoleLog).toHaveBeenCalledWith('Credential "Test Instance" removed successfully.');
    });

    it('should handle non-existent credential', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const responses = ['Non-existent'];

      let responseIndex = 0;
      let callCount = 0;

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('3'); // Remove credential
          } else {
            callback('6'); // Exit
          }
        } else if (responseIndex < responses.length) {
          callback(responses[responseIndex++]);
        }
        return mockRl;
      });

      mockManager.getCredentialByName.mockReturnValue(undefined);

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('Credential "Non-existent" not found.');
      expect(mockManager.removeCredential).not.toHaveBeenCalled();
    });

    it('should cancel removal when user declines', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const existingCredential = {
        name: 'Test Instance',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        token: 'token123',
        puppeteerLogin: false
      };

      const responses = [
        'Test Instance',           // name
        'n'                       // decline removal
      ];

      let responseIndex = 0;
      let callCount = 0;

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('3'); // Remove credential
          } else {
            callback('6'); // Exit
          }
        } else if (responseIndex < responses.length) {
          callback(responses[responseIndex++]);
        }
        return mockRl;
      });

      mockManager.getCredentialByName.mockReturnValue(existingCredential);

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('Removal cancelled.');
      expect(mockManager.removeCredential).not.toHaveBeenCalled();
    });
  });

  describe('Import/Export Functions', () => {
    beforeEach(() => {
      mockAskPassword.mockResolvedValue('password123');
      mockManager.initialize.mockResolvedValue();
    });

    it('should import credentials from JSON file', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const mockCredentials = [
        {
          name: 'Imported Instance',
          baseUrl: 'https://imported.atlassian.net',
          spaceKey: 'IMP',
          username: 'imported',
          token: 'imported-token',
          puppeteerLogin: false
        }
      ];

      const responses = ['import.json'];

      let responseIndex = 0;
      let callCount = 0;

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('4'); // Import from JSON
          } else {
            callback('6'); // Exit
          }
        } else if (responseIndex < responses.length) {
          callback(responses[responseIndex++]);
        }
        return mockRl;
      });

      mockFs.readFile.mockResolvedValue(JSON.stringify({ credentials: mockCredentials }));
      mockManager.importCredentials.mockResolvedValue();

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockFs.readFile).toHaveBeenCalledWith('import.json', 'utf8');
      expect(mockManager.importCredentials).toHaveBeenCalledWith(mockCredentials);
      expect(mockConsoleLog).toHaveBeenCalledWith('Credentials imported successfully.');
    });

    it('should export credentials to JSON file', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const mockCredentials = [
        {
          name: 'Export Instance',
          baseUrl: 'https://export.atlassian.net',
          spaceKey: 'EXP',
          username: 'export',
          token: 'export-token',
          puppeteerLogin: false
        }
      ];

      const responses = ['export.json'];

      let responseIndex = 0;
      let callCount = 0;

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('5'); // Export to JSON
          } else {
            callback('6'); // Exit
          }
        } else if (responseIndex < responses.length) {
          callback(responses[responseIndex++]);
        }
        return mockRl;
      });

      mockManager.exportCredentials.mockReturnValue(mockCredentials);
      mockFs.writeFile.mockResolvedValue();

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockManager.exportCredentials).toHaveBeenCalled();
      expect(mockFs.writeFile).toHaveBeenCalledWith(
        'export.json',
        JSON.stringify({ credentials: mockCredentials }, null, 2),
        'utf8'
      );
      expect(mockConsoleLog).toHaveBeenCalledWith('Credentials exported to export.json');
    });

    it('should handle import file read errors', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const responses = ['nonexistent.json'];

      let responseIndex = 0;
      let callCount = 0;

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('4'); // Import from JSON
          } else {
            callback('6'); // Exit
          }
        } else if (responseIndex < responses.length) {
          callback(responses[responseIndex++]);
        }
        return mockRl;
      });

      mockFs.readFile.mockRejectedValue(new Error('File not found'));

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('Error reading file: File not found');
    });

    it('should handle invalid JSON format', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const responses = ['invalid.json'];

      let responseIndex = 0;
      let callCount = 0;

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('4'); // Import from JSON
          } else {
            callback('6'); // Exit
          }
        } else if (responseIndex < responses.length) {
          callback(responses[responseIndex++]);
        }
        return mockRl;
      });

      mockFs.readFile.mockResolvedValue('invalid json');

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith(expect.stringContaining('Error parsing JSON'));
    });

    it('should handle export file write errors', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      const responses = ['/invalid/path/export.json'];

      let responseIndex = 0;
      let callCount = 0;

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callCount++;
          if (callCount === 1) {
            callback('5'); // Export to JSON
          } else {
            callback('6'); // Exit
          }
        } else if (responseIndex < responses.length) {
          callback(responses[responseIndex++]);
        }
        return mockRl;
      });

      mockManager.exportCredentials.mockReturnValue([]);
      mockFs.writeFile.mockRejectedValue(new Error('Permission denied'));

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('Error writing file: Permission denied');
    });
  });

  describe('Process stdin handling', () => {
    beforeEach(() => {
      mockAskPassword.mockResolvedValue('password123');
      mockManager.initialize.mockResolvedValue();
    });

    it('should resume stdin if paused', async () => {
      process.argv = ['node', 'credential-manager-cli.js'];

      mockProcessStdin.isPaused.mockReturnValue(true);

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('Select an option')) {
          callback('6'); // Exit option
        }
        return mockRl;
      });

      const { main } = await import('../../src/cli/credential-manager-cli');
      await main();

      expect(mockProcessStdin.isPaused).toHaveBeenCalled();
      expect(mockProcessStdin.resume).toHaveBeenCalled();
    });
  });
});
