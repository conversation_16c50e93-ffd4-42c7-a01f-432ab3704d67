/**
 * Core logic for credential manager CL<PERSON> (testable)
 */

import { SecureCredentialsManager, ConfluenceCredential } from '../services/secure-credentials-manager';
import { askPassword } from '../utils/password-input';
import { logger } from '../utils/logger';
import * as readline from 'readline';
import * as path from 'path';
import * as fs from 'fs/promises';

export interface CLIOptions {
  importFilePath?: string;
  showHelp?: boolean;
}

export class CredentialManagerCLI {
  private rl?: readline.Interface;
  private manager: SecureCredentialsManager;

  constructor() {
    this.manager = new SecureCredentialsManager();
  }

  /**
   * Parse command line arguments
   */
  parseArguments(args: string[]): CLIOptions {
    const options: CLIOptions = {};

    for (let i = 0; i < args.length; i++) {
      if (args[i] === '--import' || args[i] === '-i') {
        if (i + 1 < args.length) {
          options.importFilePath = args[i + 1];
          i++; // Skip the next argument as it's the file path
        } else {
          throw new Error('--import flag requires a file path argument.');
        }
      } else if (args[i] === '--help' || args[i] === '-h') {
        options.showHelp = true;
      }
    }

    return options;
  }

  /**
   * Show help message
   */
  getHelpMessage(): string {
    return [
      'Usage: credential-manager-cli [options]',
      '',
      'Options:',
      '  --import, -i <file>  Import credentials from specified JSON file',
      '  --help, -h           Show this help message',
      '',
      'Examples:',
      '  credential-manager-cli --import ./my-credentials.json',
      '  credential-manager-cli -i /path/to/config.json'
    ].join('\n');
  }

  /**
   * Import credentials from file
   */
  async importCredentials(filePath: string): Promise<number> {
    try {
      console.log(`Importing from: ${filePath}`);

      // Read and validate file
      const fileContent = await fs.readFile(filePath, 'utf8');
      let credentials: ConfluenceCredential[];

      try {
        const parsed = JSON.parse(fileContent);
        credentials = Array.isArray(parsed) ? parsed : parsed.credentials || [];
      } catch (error) {
        throw new Error('Invalid JSON format in file.');
      }

      if (!Array.isArray(credentials)) {
        throw new Error('File must contain an array of credentials or an object with a credentials array.');
      }

      // Validate credentials
      for (const cred of credentials) {
        if (!cred || typeof cred !== 'object' || !cred.name || !cred.baseUrl) {
          throw new Error('Each credential must have at least name and baseUrl properties.');
        }
      }

      console.log(`Found ${credentials.length} credential(s) in file.`);

      if (credentials.length === 0) {
        console.log('No credentials to import.');
        return 0;
      }

      // Show preview
      console.log('\nCredentials to import:');
      credentials.forEach((cred, index) => {
        console.log(`  ${index + 1}. ${cred.name} (${cred.baseUrl})`);
      });

      // Initialize manager and import
      await this.manager.initialize();

      for (const credential of credentials) {
        await this.manager.addOrUpdateCredential(credential);
      }

      console.log(`\nSuccessfully imported ${credentials.length} credential(s).`);
      return credentials.length;

    } catch (error) {
      if (error instanceof Error && error.message.includes('ENOENT')) {
        throw new Error(`File not found: ${filePath}`);
      }
      throw error;
    }
  }

  /**
   * Create readline interface
   */
  private createReadlineInterface(): readline.Interface {
    if (!this.rl) {
      this.rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
    }
    return this.rl;
  }

  /**
   * Ask a question
   */
  private askQuestion(query: string): Promise<string> {
    const rl = this.createReadlineInterface();
    return new Promise(resolve => rl.question(query, resolve));
  }

  /**
   * Initialize manager with retry mechanism
   */
  async initializeManager(): Promise<void> {
    const maxAttempts = 3;
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        attempts++;
        console.log(`\nInitializing credential manager... (Attempt ${attempts}/${maxAttempts})`);

        const password = await askPassword('Enter master password (leave empty for unencrypted storage): ');
        await this.manager.initialize(password || undefined);

        console.log('✓ Credential manager initialized successfully');
        return;

      } catch (error) {
        console.error(`✗ Failed to initialize credential manager: ${error instanceof Error ? error.message : error}`);

        if (attempts >= maxAttempts) {
          throw new Error(`Maximum attempts (${maxAttempts}) reached. Unable to initialize credential manager.`);
        }

        console.log('Please try again...');
      }
    }
  }

  /**
   * Show main menu
   */
  async showMainMenu(): Promise<void> {
    while (true) {
      console.log('\n=== Credential Manager ===');
      console.log('1. List all credentials');
      console.log('2. Add new credential');
      console.log('3. Remove credential');
      console.log('4. Import from JSON file');
      console.log('5. Export to JSON file');
      console.log('6. Exit');

      const choice = await this.askQuestion('\nSelect an option (1-6): ');

      try {
        switch (choice.trim()) {
          case '1':
            await this.listCredentials();
            break;
          case '2':
            await this.addCredential();
            break;
          case '3':
            await this.removeCredential();
            break;
          case '4':
            await this.importFromFile();
            break;
          case '5':
            await this.exportToFile();
            break;
          case '6':
            console.log('Exiting...');
            return;
          default:
            console.log('Invalid option. Please try again.');
        }
      } catch (error) {
        console.error(`Error: ${error instanceof Error ? error.message : error}`);
      }
    }
  }

  /**
   * List all credentials
   */
  private async listCredentials(): Promise<void> {
    const credentials = this.manager.getCredentials();

    if (credentials.length === 0) {
      console.log('No credentials stored.');
      return;
    }

    console.log(`\nStored credentials (${credentials.length}):`);
    credentials.forEach((cred, index) => {
      console.log(`${index + 1}. ${cred.name}`);
      console.log(`   URL: ${cred.baseUrl}`);
      console.log(`   Space: ${cred.spaceKey || 'Not specified'}`);
      console.log(`   Auth: ${cred.puppeteerLogin ? 'Browser Login' : 'API Token'}`);
      console.log('');
    });
  }

  /**
   * Add new credential
   */
  private async addCredential(): Promise<void> {
    console.log('\n=== Add New Credential ===');

    const name = await this.askQuestion('Enter credential name: ');
    const baseUrl = await this.askQuestion('Enter Confluence base URL: ');
    const spaceKey = await this.askQuestion('Enter space key (optional): ');

    console.log('\nAuthentication method:');
    console.log('1. API Token');
    console.log('2. Browser Login (Puppeteer)');
    const authChoice = await this.askQuestion('Select authentication method (1-2): ');

    let token: string | undefined;
    let puppeteerLogin = false;

    if (authChoice.trim() === '1') {
      token = await this.askQuestion('Enter API token: ');
    } else if (authChoice.trim() === '2') {
      puppeteerLogin = true;
    } else {
      throw new Error('Invalid authentication method selected.');
    }

    const credential: ConfluenceCredential = {
      name: name.trim(),
      baseUrl: baseUrl.trim(),
      spaceKey: spaceKey.trim() || 'DEFAULT',
      token,
      puppeteerLogin
    };

    await this.manager.addOrUpdateCredential(credential);
    console.log('✓ Credential added successfully');
  }

  /**
   * Remove credential
   */
  private async removeCredential(): Promise<void> {
    const credentials = this.manager.getCredentials();

    if (credentials.length === 0) {
      console.log('No credentials to remove.');
      return;
    }

    console.log('\n=== Remove Credential ===');
    console.log('Select credential to remove:');

    credentials.forEach((cred, index) => {
      console.log(`${index + 1}. ${cred.name} (${cred.baseUrl})`);
    });

    const choice = await this.askQuestion('\nEnter number of credential to remove: ');
    const index = parseInt(choice.trim()) - 1;

    if (index < 0 || index >= credentials.length) {
      console.log('Invalid selection.');
      return;
    }

    const credential = credentials[index];
    const confirm = await this.askQuestion(`Are you sure you want to remove "${credential.name}"? (y/N): `);

    if (confirm.toLowerCase() === 'y' || confirm.toLowerCase() === 'yes') {
      const removed = await this.manager.removeCredential(credential.baseUrl);
      if (removed) {
        console.log('✓ Credential removed successfully');
      } else {
        console.log('✗ Failed to remove credential');
      }
    } else {
      console.log('Removal cancelled.');
    }
  }

  /**
   * Import from file (interactive)
   */
  private async importFromFile(): Promise<void> {
    const filePath = await this.askQuestion('Enter path to JSON file: ');

    try {
      const fileContent = await fs.readFile(filePath.trim(), 'utf8');
      const parsed = JSON.parse(fileContent);
      const credentials = Array.isArray(parsed) ? parsed : parsed.credentials || [];

      console.log(`Found ${credentials.length} credential(s) in file.`);

      if (credentials.length > 0) {
        console.log('\nCredentials to import:');
        credentials.forEach((cred: any, index: number) => {
          console.log(`  ${index + 1}. ${cred.name} (${cred.baseUrl})`);
        });

        const confirm = await this.askQuestion('\nProceed with import? (y/N): ');

        if (confirm.toLowerCase() === 'y' || confirm.toLowerCase() === 'yes') {
          await this.importCredentials(filePath.trim());
        } else {
          console.log('Import cancelled.');
        }
      }
    } catch (error) {
      if (error instanceof Error && error.message.includes('ENOENT')) {
        console.error('Error: File not found.');
      } else if (error instanceof SyntaxError) {
        console.error('Error: Invalid JSON format in file.');
      } else {
        console.error(`Error: ${error instanceof Error ? error.message : error}`);
      }
    }
  }

  /**
   * Export to file
   */
  private async exportToFile(): Promise<void> {
    const credentials = this.manager.getCredentials();

    if (credentials.length === 0) {
      console.log('No credentials to export.');
      return;
    }

    const filePath = await this.askQuestion('Enter path for export file: ');

    try {
      const exportData = {
        credentials,
        exportedAt: new Date().toISOString(),
        version: '2.0'
      };

      await fs.writeFile(filePath.trim(), JSON.stringify(exportData, null, 2), 'utf8');
      console.log(`✓ Exported ${credentials.length} credential(s) to ${filePath}`);
    } catch (error) {
      console.error(`Error exporting credentials: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.rl) {
      this.rl.close();
    }
    await this.manager.cleanup();
  }
}
