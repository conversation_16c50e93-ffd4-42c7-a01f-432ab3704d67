#!/usr/bin/env node
/**
 * CLI tool for managing Confluence credentials
 */

import { CredentialManagerCLI } from './credential-manager-core';

async function main(): Promise<void> {
  const cli = new CredentialManagerCLI();
  
  try {
    console.log('Confluence Credential Manager');
    console.log('=============================');

    // Parse command line arguments
    const args = process.argv.slice(2);
    const options = cli.parseArguments(args);

    // Handle help flag
    if (options.showHelp) {
      console.log(cli.getHelpMessage());
      process.exit(0);
    }

    // Handle import flag
    if (options.importFilePath) {
      await cli.importCredentials(options.importFilePath);
      process.exit(0);
    }

    // Initialize manager and show interactive menu
    await cli.initializeManager();
    await cli.showMainMenu();

  } catch (error) {
    console.error(`Error: ${error instanceof Error ? error.message : error}`);
    process.exit(1);
  } finally {
    await cli.cleanup();
  }
}

// Only run main if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('\nUnexpected error occurred:', error instanceof Error ? error.message : error);
    console.error('The application will now exit. Please try again.');
    process.exit(1);
  });
}

// Export for testing
export { main };
