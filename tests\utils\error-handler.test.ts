/**
 * Tests for Error Handler utility
 */

import { withErrorHandling, ErrorHandlingOptions } from '../../src/utils/error-handler';
import { AppError, ValidationError, NetworkError, AuthenticationError, ApplicationError } from '../../src/errors';

// Mock console and process
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation();
const mockConsoleDebug = jest.spyOn(console, 'debug').mockImplementation();
const mockProcessExit = jest.spyOn(process, 'exit').mockImplementation();

describe('Error Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    mockConsoleError.mockRestore();
    mockConsoleDebug.mockRestore();
    mockProcessExit.mockRestore();
  });

  describe('withErrorHandling', () => {
    it('should execute operation successfully', async () => {
      const operation = jest.fn().mockResolvedValue('success');
      const options: ErrorHandlingOptions = {
        context: 'test operation',
        exitOnError: false
      };

      const result = await withErrorHandling(operation, options);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
      expect(mockConsoleError).not.toHaveBeenCalled();
    });

    it('should handle AppError with user message', async () => {
      const error = new ValidationError('Internal validation error', 'Please check your input');
      const operation = jest.fn().mockRejectedValue(error);
      const options: ErrorHandlingOptions = {
        context: 'validation',
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(error);

      expect(mockConsoleError).toHaveBeenCalledWith('Please check your input');
      expect(mockConsoleDebug).toHaveBeenCalledWith('Error details:', expect.objectContaining({
        context: 'validation',
        error: 'Internal validation error',
        code: 'VALIDATION_ERROR'
      }));
    });

    it('should handle generic Error', async () => {
      const error = new Error('Generic error message');
      const operation = jest.fn().mockRejectedValue(error);
      const options: ErrorHandlingOptions = {
        context: 'generic operation',
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(ApplicationError);

      expect(mockConsoleError).toHaveBeenCalledWith('An unexpected error occurred. Please check the logs for more details. (generic operation)');
      expect(mockConsoleDebug).toHaveBeenCalledWith('Error details:', expect.objectContaining({
        context: 'generic operation',
        error: 'Generic error message'
      }));
    });

    it('should handle string errors', async () => {
      const operation = jest.fn().mockRejectedValue('String error');
      const options: ErrorHandlingOptions = {
        context: 'string error test',
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(ApplicationError);

      expect(mockConsoleError).toHaveBeenCalledWith('An unexpected error occurred. Please check the logs for more details. (string error test)');
    });

    it('should handle unknown error types', async () => {
      const operation = jest.fn().mockRejectedValue({ unknown: 'object' });
      const options: ErrorHandlingOptions = {
        context: 'unknown error test',
        exitOnError: false
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(ApplicationError);

      expect(mockConsoleError).toHaveBeenCalledWith('An unexpected error occurred. Please check the logs for more details. (unknown error test)');
    });

    it('should use custom error handler', async () => {
      const error = new ValidationError('Test error', 'User message');
      const operation = jest.fn().mockRejectedValue(error);
      const customHandler = jest.fn().mockReturnValue('custom result');
      const options: ErrorHandlingOptions = {
        context: 'custom handler test',
        exitOnError: false,
        onError: customHandler
      };

      const result = await withErrorHandling(operation, options);

      expect(result).toBe('custom result');
      expect(customHandler).toHaveBeenCalledWith(error);
    });

    it('should use custom logger', async () => {
      const error = new NetworkError('Network failed', 'Connection error');
      const operation = jest.fn().mockRejectedValue(error);
      const mockLogger = {
        error: jest.fn(),
        debug: jest.fn()
      };
      const options: ErrorHandlingOptions = {
        context: 'custom logger test',
        exitOnError: false,
        logger: mockLogger
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(error);

      expect(mockLogger.error).toHaveBeenCalledWith('Connection error');
      expect(mockLogger.debug).toHaveBeenCalledWith('Error details:', expect.any(Object));
    });

    it('should handle silent mode', async () => {
      const error = new AuthenticationError('Auth failed', 'Login error');
      const operation = jest.fn().mockRejectedValue(error);
      const options: ErrorHandlingOptions = {
        context: 'silent test',
        exitOnError: false,
        silent: true
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(error);

      expect(mockConsoleError).not.toHaveBeenCalled();
      expect(mockConsoleDebug).not.toHaveBeenCalled();
    });



    it('should handle logger without debug method', async () => {
      const error = new ValidationError('Test error', 'User message');
      const operation = jest.fn().mockRejectedValue(error);
      const mockLogger = {
        error: jest.fn()
        // No debug method
      };
      const options: ErrorHandlingOptions = {
        context: 'logger without debug',
        exitOnError: false,
        logger: mockLogger
      };

      await expect(withErrorHandling(operation, options)).rejects.toThrow(error);

      expect(mockLogger.error).toHaveBeenCalledWith('User message');
      // Should not crash when debug method is missing
    });
  });


});
