/**
 * Tests for Lazy Puppeteer Manager service
 */

import { LazyPuppeteerManager, lazyPuppeteerManager } from '../../src/services/lazy-puppeteer-manager';
import { PerformanceUtils } from '../../src/utils/performance-utils';
import { logger } from '../../src/utils/logger';
import { withErrorHandling } from '../../src/utils/error-handler';
import { defaultConfig } from '../../src/config/app-config';

// Mock dependencies
jest.mock('../../src/utils/performance-utils');
jest.mock('../../src/utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));
jest.mock('../../src/utils/error-handler');
jest.mock('../../src/config/app-config', () => ({
  defaultConfig: {
    http: {
      userAgent: 'test-user-agent'
    }
  }
}));

// Mock Puppeteer
const mockPuppeteer = {
  launch: jest.fn(),
  default: jest.fn()
};

const mockBrowser = {
  newPage: jest.fn(),
  close: jest.fn(),
  on: jest.fn(),
  disconnect: jest.fn(),
  pages: jest.fn().mockResolvedValue([])
};

const mockPage = {
  setDefaultTimeout: jest.fn(),
  setDefaultNavigationTimeout: jest.fn(),
  setRequestInterception: jest.fn(),
  on: jest.fn(),
  goto: jest.fn(),
  close: jest.fn(),
  setCookie: jest.fn(),
  cookies: jest.fn(),
  url: jest.fn(),
  waitForSelector: jest.fn(),
  click: jest.fn(),
  type: jest.fn(),
  evaluate: jest.fn()
};

describe('LazyPuppeteerManager', () => {
  let manager: LazyPuppeteerManager;
  let mockLazyModule: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock lazy module
    mockLazyModule = {
      load: jest.fn().mockResolvedValue(mockPuppeteer),
      isLoaded: jest.fn().mockReturnValue(false),
      unload: jest.fn()
    };

    // Mock PerformanceUtils
    (PerformanceUtils.createLazyModule as jest.Mock).mockReturnValue(mockLazyModule);
    (PerformanceUtils.registerCleanup as jest.Mock).mockImplementation();
    (PerformanceUtils.logMemoryUsage as jest.Mock).mockImplementation();
    (PerformanceUtils.withTimeout as jest.Mock).mockImplementation((fn) => fn());

    // Mock withErrorHandling to execute the function and handle errors
    (withErrorHandling as jest.Mock).mockImplementation(async (fn, options) => {
      try {
        return await fn();
      } catch (error) {
        // Log the error if logger is provided
        if (options?.logger?.debug) {
          options.logger.debug(`Error in ${options.context}:`, error);
        }

        // If exitOnError is false, don't throw (simulate error handling)
        if (options?.exitOnError === false) {
          return undefined;
        }

        // Re-throw the error for test verification
        throw error;
      }
    });

    // Setup mock browser and page
    mockPuppeteer.launch.mockResolvedValue(mockBrowser);
    mockBrowser.newPage.mockResolvedValue(mockPage);

    manager = new LazyPuppeteerManager();
  });

  describe('constructor', () => {
    it('should create lazy module for puppeteer', () => {
      expect(PerformanceUtils.createLazyModule).toHaveBeenCalledWith(
        'puppeteer',
        expect.any(Function),
        expect.any(Function)
      );
    });

    it('should register cleanup', () => {
      expect(PerformanceUtils.registerCleanup).toHaveBeenCalledWith(manager);
    });
  });

  describe('launchBrowser', () => {
    it('should launch browser with default options', async () => {
      const browser = await manager.launchBrowser();

      expect(mockLazyModule.load).toHaveBeenCalled();
      expect(mockPuppeteer.launch).toHaveBeenCalledWith({
        headless: false,
        defaultViewport: null,
        args: expect.arrayContaining([
          '--start-maximized',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--user-agent=test-user-agent'
        ])
      });
      expect(browser).toBe(mockBrowser);
    });

    it('should merge custom options with defaults', async () => {
      const customOptions = {
        headless: true,
        args: ['--custom-arg']
      };

      await manager.launchBrowser(customOptions);

      expect(mockPuppeteer.launch).toHaveBeenCalledWith({
        headless: true,
        defaultViewport: null,
        args: ['--custom-arg'],
      });
    });

    it('should track active browsers', async () => {
      const browser = await manager.launchBrowser();

      // Verify browser is tracked
      expect(manager['activeBrowsers'].has(browser)).toBe(true);
    });

    it('should setup browser disconnect handler', async () => {
      await manager.launchBrowser();

      expect(mockBrowser.on).toHaveBeenCalledWith('disconnected', expect.any(Function));
    });

    it('should log memory usage and debug information', async () => {
      await manager.launchBrowser();

      expect(PerformanceUtils.logMemoryUsage).toHaveBeenCalledWith('before-browser-launch');
      expect(PerformanceUtils.logMemoryUsage).toHaveBeenCalledWith('after-browser-launch');
      expect(logger.debug).toHaveBeenCalledWith('Launching browser with options:', expect.any(Object));
    });

    it('should use timeout for browser launch', async () => {
      await manager.launchBrowser();

      expect(PerformanceUtils.withTimeout).toHaveBeenCalledWith(
        expect.any(Function),
        30000,
        'Browser launch timed out'
      );
    });
  });

  describe('createPage', () => {
    let browser: any;

    beforeEach(async () => {
      browser = await manager.launchBrowser();
    });

    it('should create new page from browser', async () => {
      const page = await manager.createPage(browser);

      expect(mockBrowser.newPage).toHaveBeenCalled();
      expect(page).toBe(mockPage);
    });

    it('should track active pages', async () => {
      const page = await manager.createPage(browser);

      expect(manager['activePages'].has(page)).toBe(true);
    });

    it('should set page timeouts', async () => {
      await manager.createPage(browser);

      expect(mockPage.setDefaultTimeout).toHaveBeenCalledWith(30000);
      expect(mockPage.setDefaultNavigationTimeout).toHaveBeenCalledWith(30000);
    });

    it('should setup page close handler', async () => {
      await manager.createPage(browser);

      expect(mockPage.on).toHaveBeenCalledWith('close', expect.any(Function));
    });

    it('should setup request interception in fast mode', async () => {
      process.env.PUPPETEER_FAST_MODE = 'true';

      await manager.createPage(browser);

      expect(mockPage.setRequestInterception).toHaveBeenCalledWith(true);
      expect(mockPage.on).toHaveBeenCalledWith('request', expect.any(Function));

      delete process.env.PUPPETEER_FAST_MODE;
    });

    it('should log debug information', async () => {
      await manager.createPage(browser);

      expect(logger.debug).toHaveBeenCalledWith(expect.stringContaining('Page created'));
    });
  });

  describe('navigateToUrl', () => {
    let page: any;

    beforeEach(async () => {
      const browser = await manager.launchBrowser();
      page = await manager.createPage(browser);
    });

    it('should navigate to URL with timeout', async () => {
      const url = 'https://example.com';
      const mockResponse = {
        ok: jest.fn().mockReturnValue(true),
        status: jest.fn().mockReturnValue(200)
      };
      mockPage.goto.mockResolvedValue(mockResponse);

      await manager.navigateToUrl(page, url);

      expect(PerformanceUtils.withTimeout).toHaveBeenCalledWith(
        expect.any(Function),
        35000, // timeout + 5000
        `Navigation to ${url} timed out`
      );
    });

    it('should log navigation attempt', async () => {
      const url = 'https://example.com';
      const mockResponse = {
        ok: jest.fn().mockReturnValue(true),
        status: jest.fn().mockReturnValue(200)
      };
      mockPage.goto.mockResolvedValue(mockResponse);

      await manager.navigateToUrl(page, url);

      expect(logger.debug).toHaveBeenCalledWith(`Navigating to: ${url}`);
    });
  });

  describe('closePage', () => {
    let page: any;

    beforeEach(async () => {
      const browser = await manager.launchBrowser();
      page = await manager.createPage(browser);
    });

    it('should close page and remove from tracking', async () => {
      mockPage.close.mockResolvedValue(undefined);

      await manager.closePage(page);

      expect(mockPage.close).toHaveBeenCalled();
      expect(manager['activePages'].has(page)).toBe(false);
    });

    it('should handle page close errors gracefully', async () => {
      mockPage.close.mockRejectedValue(new Error('Close failed'));

      // The error should be caught and logged, not thrown
      await manager.closePage(page);

      expect(logger.debug).toHaveBeenCalledWith('Error in page closing:', expect.any(Error));
    });
  });

  describe('closeBrowser', () => {
    let browser: any;

    beforeEach(async () => {
      browser = await manager.launchBrowser();
    });

    it('should close browser and remove from tracking', async () => {
      mockBrowser.close.mockResolvedValue(undefined);

      await manager.closeBrowser(browser);

      expect(mockBrowser.close).toHaveBeenCalled();
      expect(manager['activeBrowsers'].has(browser)).toBe(false);
    });

    it('should handle browser close errors gracefully', async () => {
      mockBrowser.close.mockRejectedValue(new Error('Close failed'));

      await manager.closeBrowser(browser);

      expect(logger.debug).toHaveBeenCalledWith(expect.stringContaining('Error closing browser'));
    });
  });

  describe('cleanup', () => {
    it('should close all active pages and browsers', async () => {
      const browser = await manager.launchBrowser();
      const page = await manager.createPage(browser);

      mockPage.close.mockResolvedValue(undefined);
      mockBrowser.close.mockResolvedValue(undefined);

      await manager.cleanup();

      expect(mockPage.close).toHaveBeenCalled();
      expect(mockBrowser.close).toHaveBeenCalled();
    });

    it('should clear tracking sets after cleanup', async () => {
      const browser = await manager.launchBrowser();
      const page = await manager.createPage(browser);

      mockPage.close.mockResolvedValue(undefined);
      mockBrowser.close.mockResolvedValue(undefined);

      await manager.cleanup();

      expect(manager['activePages'].size).toBe(0);
      expect(manager['activeBrowsers'].size).toBe(0);
    });

    it('should handle cleanup errors gracefully', async () => {
      const browser = await manager.launchBrowser();
      const page = await manager.createPage(browser);

      mockPage.close.mockRejectedValue(new Error('Page close failed'));
      mockBrowser.close.mockRejectedValue(new Error('Browser close failed'));

      await manager.cleanup();

      expect(logger.debug).toHaveBeenCalledWith('Error in page closing:', expect.any(Error));
      expect(logger.debug).toHaveBeenCalledWith('Error in browser closing:', expect.any(Error));
    });
  });

  describe('getStats', () => {
    it('should return current statistics', async () => {
      const browser = await manager.launchBrowser();
      const page = await manager.createPage(browser);

      mockLazyModule.isLoaded.mockReturnValue(true);

      const stats = manager.getStats();

      expect(stats).toEqual({
        activeBrowsers: 1,
        activePages: 1,
        moduleLoaded: true
      });
    });

    it('should return zero stats when nothing is active', () => {
      const stats = manager.getStats();

      expect(stats).toEqual({
        activeBrowsers: 0,
        activePages: 0,
        moduleLoaded: false
      });
    });
  });
});

describe('lazyPuppeteerManager singleton', () => {
  it('should export a singleton instance', () => {
    expect(lazyPuppeteerManager).toBeInstanceOf(LazyPuppeteerManager);
  });
});
