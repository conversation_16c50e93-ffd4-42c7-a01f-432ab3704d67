/**
 * Tests for Update Confluence Page CLI
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import * as readline from 'readline';
import { marked } from 'marked';
import { createConfluenceHttpClient } from '../../src/services/http-client';
import { SecureCredentialsManager } from '../../src/services/secure-credentials-manager';
import { SecureCookieManager } from '../../src/services/secure-cookie-manager';
import { lazyPuppeteerManager } from '../../src/services/lazy-puppeteer-manager';
import { logger } from '../../src/utils/logger';
import { askPassword } from '../../src/utils/password-input';
import { PerformanceUtils } from '../../src/utils/performance-utils';
import { FileUtils } from '../../src/utils/file-utils';
import { withErrorHandling } from '../../src/utils/error-handler';

// Mock dependencies
jest.mock('fs/promises');
jest.mock('readline');
jest.mock('marked');
jest.mock('../../src/services/http-client');
jest.mock('../../src/services/secure-credentials-manager');
jest.mock('../../src/services/secure-cookie-manager');
jest.mock('../../src/services/lazy-puppeteer-manager');
jest.mock('../../src/utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));
jest.mock('../../src/utils/password-input');
jest.mock('../../src/utils/performance-utils');
jest.mock('../../src/utils/file-utils');
jest.mock('../../src/utils/error-handler');

// Mock console methods
const mockConsoleLog = jest.spyOn(console, 'log').mockImplementation();
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation();

// Mock process methods
const mockProcessExit = jest.spyOn(process, 'exit').mockImplementation();
const mockProcessStdin = {
  isTTY: true,
  setRawMode: jest.fn(),
  isPaused: jest.fn(),
  resume: jest.fn()
};
Object.defineProperty(process, 'stdin', {
  value: mockProcessStdin,
  writable: true
});

// Mock process.argv
const originalArgv = process.argv;

describe('Update Confluence Page CLI', () => {
  let mockCredentialsManager: jest.Mocked<SecureCredentialsManager>;
  let mockCookieManager: jest.Mocked<SecureCookieManager>;
  let mockLazyPuppeteerManager: any;
  let mockHttpClient: any;
  let mockRl: jest.Mocked<readline.Interface>;
  let mockFs: jest.Mocked<typeof fs>;
  let mockMarked: jest.MockedFunction<typeof marked>;
  let mockAskPassword: jest.MockedFunction<typeof askPassword>;
  let mockFileUtils: jest.Mocked<typeof FileUtils>;
  let mockWithErrorHandling: jest.MockedFunction<typeof withErrorHandling>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset process.argv
    process.argv = ['node', 'update-confluence-page-cli.js'];

    // Mock fs
    mockFs = fs as jest.Mocked<typeof fs>;

    // Mock readline
    mockRl = {
      question: jest.fn(),
      close: jest.fn(),
      on: jest.fn()
    } as any;
    (readline.createInterface as jest.Mock).mockReturnValue(mockRl);

    // Mock marked
    mockMarked = marked as jest.MockedFunction<typeof marked>;

    // Mock HTTP client
    mockHttpClient = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn()
    };
    (createConfluenceHttpClient as jest.Mock).mockReturnValue(mockHttpClient);

    // Mock credentials manager
    mockCredentialsManager = {
      initialize: jest.fn(),
      cleanup: jest.fn(),
      getCredentials: jest.fn(),
      getCredentialByBaseUrl: jest.fn()
    } as any;
    (SecureCredentialsManager as jest.Mock).mockImplementation(() => mockCredentialsManager);

    // Mock cookie manager
    mockCookieManager = {
      initialize: jest.fn(),
      cleanup: jest.fn(),
      hasCookiesForBaseUrl: jest.fn(),
      getCookiesForBaseUrl: jest.fn()
    } as any;
    (SecureCookieManager as jest.Mock).mockImplementation(() => mockCookieManager);

    // Mock lazy puppeteer manager
    mockLazyPuppeteerManager = {
      cleanup: jest.fn(),
      getStats: jest.fn().mockReturnValue({ activeBrowsers: 0, activePages: 0, moduleLoaded: false })
    };
    (lazyPuppeteerManager as any) = mockLazyPuppeteerManager;

    // Mock askPassword
    mockAskPassword = askPassword as jest.MockedFunction<typeof askPassword>;

    // Mock FileUtils
    mockFileUtils = FileUtils as jest.Mocked<typeof FileUtils>;
    mockFileUtils.readFile = jest.fn();
    mockFileUtils.fileExists = jest.fn();

    // Mock PerformanceUtils
    (PerformanceUtils.logMemoryUsage as jest.Mock).mockImplementation();
    (PerformanceUtils.executeCleanup as jest.Mock).mockResolvedValue();

    // Mock withErrorHandling
    mockWithErrorHandling = withErrorHandling as jest.MockedFunction<typeof withErrorHandling>;
    mockWithErrorHandling.mockImplementation(async (fn) => await fn());

    // Mock process.stdin
    mockProcessStdin.isTTY = true;
    mockProcessStdin.isPaused.mockReturnValue(false);
  });

  afterEach(() => {
    process.argv = originalArgv;
  });

  afterAll(() => {
    mockConsoleLog.mockRestore();
    mockConsoleError.mockRestore();
    mockProcessExit.mockRestore();
  });

  describe('Command Line Arguments', () => {
    it('should show help when --help flag is provided', async () => {
      process.argv = ['node', 'update-confluence-page-cli.js', '--help'];

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('Usage: update-confluence-page-cli [options] <markdown-file>');
      expect(mockConsoleLog).toHaveBeenCalledWith('Options:');
      expect(mockConsoleLog).toHaveBeenCalledWith('  --page-id <id>       Confluence page ID to update');
      expect(mockConsoleLog).toHaveBeenCalledWith('  --base-url <url>     Confluence base URL');
      expect(mockConsoleLog).toHaveBeenCalledWith('  --help, -h           Show this help message');
      expect(mockProcessExit).toHaveBeenCalledWith(0);
    });

    it('should show help when -h flag is provided', async () => {
      process.argv = ['node', 'update-confluence-page-cli.js', '-h'];

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('Usage: update-confluence-page-cli [options] <markdown-file>');
      expect(mockProcessExit).toHaveBeenCalledWith(0);
    });

    it('should show error when no markdown file is provided', async () => {
      process.argv = ['node', 'update-confluence-page-cli.js'];

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('Error: Markdown file path is required.');
      expect(mockConsoleError).toHaveBeenCalledWith('Usage: update-confluence-page-cli [options] <markdown-file>');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });

    it('should parse page-id argument', async () => {
      process.argv = ['node', 'update-confluence-page-cli.js', '--page-id', '123456', 'test.md'];

      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('# Test Content');
      mockMarked.mockReturnValue('<h1>Test Content</h1>');
      mockAskPassword.mockResolvedValue('password123');
      mockCredentialsManager.initialize.mockResolvedValue();
      mockCookieManager.initialize.mockResolvedValue();

      // Mock interactive prompts
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('base URL')) {
          callback('https://test.atlassian.net');
        }
        return mockRl;
      });

      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        token: 'token123',
        puppeteerLogin: false
      });

      mockHttpClient.get.mockResolvedValue({
        data: {
          id: '123456',
          title: 'Test Page',
          version: { number: 1 },
          body: { storage: { value: '<p>old content</p>' } }
        }
      });

      mockHttpClient.put.mockResolvedValue({
        data: { id: '123456', title: 'Test Page' }
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockHttpClient.get).toHaveBeenCalledWith('/rest/api/content/123456?expand=body.storage,version');
    });

    it('should parse base-url argument', async () => {
      process.argv = ['node', 'update-confluence-page-cli.js', '--base-url', 'https://custom.atlassian.net', 'test.md'];

      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('# Test Content');
      mockMarked.mockReturnValue('<h1>Test Content</h1>');
      mockAskPassword.mockResolvedValue('password123');
      mockCredentialsManager.initialize.mockResolvedValue();
      mockCookieManager.initialize.mockResolvedValue();

      // Mock interactive prompts
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('page ID')) {
          callback('123456');
        }
        return mockRl;
      });

      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl: 'https://custom.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        token: 'token123',
        puppeteerLogin: false
      });

      mockHttpClient.get.mockResolvedValue({
        data: {
          id: '123456',
          title: 'Test Page',
          version: { number: 1 },
          body: { storage: { value: '<p>old content</p>' } }
        }
      });

      mockHttpClient.put.mockResolvedValue({
        data: { id: '123456', title: 'Test Page' }
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(createConfluenceHttpClient).toHaveBeenCalledWith(
        'https://custom.atlassian.net',
        expect.objectContaining({
          'Authorization': 'Basic dGVzdHVzZXI6dG9rZW4xMjM='
        })
      );
    });

    it('should handle invalid arguments', async () => {
      process.argv = ['node', 'update-confluence-page-cli.js', '--invalid-arg', 'test.md'];

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('Error: Unknown argument --invalid-arg');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });
  });

  describe('File Processing', () => {
    beforeEach(() => {
      process.argv = ['node', 'update-confluence-page-cli.js', 'test.md'];
      mockAskPassword.mockResolvedValue('password123');
      mockCredentialsManager.initialize.mockResolvedValue();
      mockCookieManager.initialize.mockResolvedValue();
    });

    it('should read and process markdown file', async () => {
      const markdownContent = '# Test Title\n\nThis is test content.';
      const htmlContent = '<h1>Test Title</h1><p>This is test content.</p>';

      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue(markdownContent);
      mockMarked.mockReturnValue(htmlContent);

      // Mock interactive prompts
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('base URL')) {
          callback('https://test.atlassian.net');
        } else if (query.includes('page ID')) {
          callback('123456');
        }
        return mockRl;
      });

      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        token: 'token123',
        puppeteerLogin: false
      });

      mockHttpClient.get.mockResolvedValue({
        data: {
          id: '123456',
          title: 'Test Page',
          version: { number: 1 },
          body: { storage: { value: '<p>old content</p>' } }
        }
      });

      mockHttpClient.put.mockResolvedValue({
        data: { id: '123456', title: 'Test Page' }
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockFileUtils.fileExists).toHaveBeenCalledWith('test.md');
      expect(mockFileUtils.readFile).toHaveBeenCalledWith('test.md');
      expect(mockMarked).toHaveBeenCalledWith(markdownContent);
    });

    it('should handle non-existent file', async () => {
      mockFileUtils.fileExists.mockResolvedValue(false);

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('Error: File "test.md" does not exist.');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });

    it('should handle file read errors', async () => {
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockRejectedValue(new Error('Permission denied'));

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('Error reading file: Permission denied');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });

    it('should extract title from markdown content', async () => {
      const markdownContent = '# Extracted Title\n\nContent here.';
      const htmlContent = '<h1>Extracted Title</h1><p>Content here.</p>';

      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue(markdownContent);
      mockMarked.mockReturnValue(htmlContent);

      // Mock interactive prompts
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('base URL')) {
          callback('https://test.atlassian.net');
        } else if (query.includes('page ID')) {
          callback('123456');
        }
        return mockRl;
      });

      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        token: 'token123',
        puppeteerLogin: false
      });

      mockHttpClient.get.mockResolvedValue({
        data: {
          id: '123456',
          title: 'Old Title',
          version: { number: 1 },
          body: { storage: { value: '<p>old content</p>' } }
        }
      });

      mockHttpClient.put.mockResolvedValue({
        data: { id: '123456', title: 'Extracted Title' }
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockHttpClient.put).toHaveBeenCalledWith(
        '/rest/api/content/123456',
        expect.objectContaining({
          title: 'Extracted Title'
        })
      );
    });
  });

  describe('Authentication', () => {
    beforeEach(() => {
      process.argv = ['node', 'update-confluence-page-cli.js', 'test.md'];
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('# Test');
      mockMarked.mockReturnValue('<h1>Test</h1>');
    });

    it('should initialize credentials manager with password', async () => {
      mockAskPassword.mockResolvedValue('master-password');
      mockCredentialsManager.initialize.mockResolvedValue();
      mockCookieManager.initialize.mockResolvedValue();

      // Mock interactive prompts
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('base URL')) {
          callback('https://test.atlassian.net');
        } else if (query.includes('page ID')) {
          callback('123456');
        }
        return mockRl;
      });

      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        token: 'token123',
        puppeteerLogin: false
      });

      mockHttpClient.get.mockResolvedValue({
        data: {
          id: '123456',
          title: 'Test Page',
          version: { number: 1 },
          body: { storage: { value: '<p>old content</p>' } }
        }
      });

      mockHttpClient.put.mockResolvedValue({
        data: { id: '123456', title: 'Test' }
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockAskPassword).toHaveBeenCalledWith('Enter master password: ');
      expect(mockCredentialsManager.initialize).toHaveBeenCalledWith('master-password');
      expect(mockCookieManager.initialize).toHaveBeenCalledWith('master-password');
    });

    it('should handle authentication cancellation', async () => {
      mockAskPassword.mockResolvedValue(''); // Empty password indicates cancellation

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith('\nOperation cancelled by user.');
      expect(mockProcessExit).toHaveBeenCalledWith(0);
    });

    it('should handle authentication errors', async () => {
      mockAskPassword.mockResolvedValue('wrong-password');
      mockCredentialsManager.initialize.mockRejectedValue(new Error('Invalid password'));

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('Authentication failed: Invalid password');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });

    it('should handle missing credentials for base URL', async () => {
      mockAskPassword.mockResolvedValue('password123');
      mockCredentialsManager.initialize.mockResolvedValue();
      mockCookieManager.initialize.mockResolvedValue();

      // Mock interactive prompts
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('base URL')) {
          callback('https://unknown.atlassian.net');
        }
        return mockRl;
      });

      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue(undefined);

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('No credentials found for https://unknown.atlassian.net');
      expect(mockConsoleError).toHaveBeenCalledWith('Please add credentials using the credential manager first.');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });
  });

  describe('Confluence API Integration', () => {
    beforeEach(() => {
      process.argv = ['node', 'update-confluence-page-cli.js', 'test.md'];
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('# Test Content');
      mockMarked.mockReturnValue('<h1>Test Content</h1>');
      mockAskPassword.mockResolvedValue('password123');
      mockCredentialsManager.initialize.mockResolvedValue();
      mockCookieManager.initialize.mockResolvedValue();

      // Mock interactive prompts
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('base URL')) {
          callback('https://test.atlassian.net');
        } else if (query.includes('page ID')) {
          callback('123456');
        }
        return mockRl;
      });

      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        token: 'token123',
        puppeteerLogin: false
      });
    });

    it('should fetch existing page and update content', async () => {
      mockHttpClient.get.mockResolvedValue({
        data: {
          id: '123456',
          title: 'Existing Title',
          version: { number: 5 },
          body: { storage: { value: '<p>old content</p>' } }
        }
      });

      mockHttpClient.put.mockResolvedValue({
        data: { id: '123456', title: 'Test Content' }
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockHttpClient.get).toHaveBeenCalledWith('/rest/api/content/123456?expand=body.storage,version');
      expect(mockHttpClient.put).toHaveBeenCalledWith(
        '/rest/api/content/123456',
        expect.objectContaining({
          id: '123456',
          type: 'page',
          title: 'Test Content',
          body: {
            storage: {
              value: '<h1>Test Content</h1>',
              representation: 'storage'
            }
          },
          version: {
            number: 6
          }
        })
      );

      expect(mockConsoleLog).toHaveBeenCalledWith('✅ Page updated successfully!');
      expect(mockConsoleLog).toHaveBeenCalledWith('📄 Page ID: 123456');
      expect(mockConsoleLog).toHaveBeenCalledWith('📝 Title: Test Content');
    });

    it('should handle page not found error', async () => {
      mockHttpClient.get.mockRejectedValue({
        response: { status: 404 },
        message: 'Page not found'
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('❌ Page with ID 123456 not found.');
      expect(mockConsoleError).toHaveBeenCalledWith('Please check the page ID and try again.');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });

    it('should handle authentication error from API', async () => {
      mockHttpClient.get.mockRejectedValue({
        response: { status: 401 },
        message: 'Unauthorized'
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('❌ Authentication failed. Please check your credentials.');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });

    it('should handle permission error from API', async () => {
      mockHttpClient.get.mockRejectedValue({
        response: { status: 403 },
        message: 'Forbidden'
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('❌ Permission denied. You may not have edit access to this page.');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });

    it('should handle network errors', async () => {
      mockHttpClient.get.mockRejectedValue({
        code: 'ECONNREFUSED',
        message: 'Connection refused'
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('❌ Network error: Connection refused');
      expect(mockConsoleError).toHaveBeenCalledWith('Please check your internet connection and Confluence URL.');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });

    it('should handle update errors', async () => {
      mockHttpClient.get.mockResolvedValue({
        data: {
          id: '123456',
          title: 'Existing Title',
          version: { number: 5 },
          body: { storage: { value: '<p>old content</p>' } }
        }
      });

      mockHttpClient.put.mockRejectedValue({
        response: { status: 409 },
        message: 'Conflict - page was modified'
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockConsoleError).toHaveBeenCalledWith('❌ Update failed: Conflict - page was modified');
      expect(mockConsoleError).toHaveBeenCalledWith('The page may have been modified by another user. Please try again.');
      expect(mockProcessExit).toHaveBeenCalledWith(1);
    });
  });

  describe('Interactive Prompts', () => {
    beforeEach(() => {
      process.argv = ['node', 'update-confluence-page-cli.js', 'test.md'];
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('# Test');
      mockMarked.mockReturnValue('<h1>Test</h1>');
      mockAskPassword.mockResolvedValue('password123');
      mockCredentialsManager.initialize.mockResolvedValue();
      mockCookieManager.initialize.mockResolvedValue();
    });

    it('should prompt for base URL when not provided', async () => {
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('base URL')) {
          callback('https://prompted.atlassian.net');
        } else if (query.includes('page ID')) {
          callback('123456');
        }
        return mockRl;
      });

      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl: 'https://prompted.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        token: 'token123',
        puppeteerLogin: false
      });

      mockHttpClient.get.mockResolvedValue({
        data: {
          id: '123456',
          title: 'Test Page',
          version: { number: 1 },
          body: { storage: { value: '<p>old content</p>' } }
        }
      });

      mockHttpClient.put.mockResolvedValue({
        data: { id: '123456', title: 'Test' }
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockRl.question).toHaveBeenCalledWith(
        'Enter Confluence base URL (e.g., https://your-domain.atlassian.net): ',
        expect.any(Function)
      );
    });

    it('should prompt for page ID when not provided', async () => {
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('base URL')) {
          callback('https://test.atlassian.net');
        } else if (query.includes('page ID')) {
          callback('789012');
        }
        return mockRl;
      });

      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        token: 'token123',
        puppeteerLogin: false
      });

      mockHttpClient.get.mockResolvedValue({
        data: {
          id: '789012',
          title: 'Test Page',
          version: { number: 1 },
          body: { storage: { value: '<p>old content</p>' } }
        }
      });

      mockHttpClient.put.mockResolvedValue({
        data: { id: '789012', title: 'Test' }
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockRl.question).toHaveBeenCalledWith(
        'Enter Confluence page ID: ',
        expect.any(Function)
      );
    });

    it('should handle readline errors', async () => {
      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('base URL')) {
          // Simulate readline error
          mockRl.on.mockImplementation((event, handler) => {
            if (event === 'error') {
              handler(new Error('Readline error'));
            }
          });
          callback('https://test.atlassian.net');
        }
        return mockRl;
      });

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(logger.error).toHaveBeenCalledWith('Readline error:', expect.any(Error));
    });
  });

  describe('Cleanup and Performance', () => {
    beforeEach(() => {
      process.argv = ['node', 'update-confluence-page-cli.js', 'test.md'];
      mockFileUtils.fileExists.mockResolvedValue(true);
      mockFileUtils.readFile.mockResolvedValue('# Test');
      mockMarked.mockReturnValue('<h1>Test</h1>');
      mockAskPassword.mockResolvedValue('password123');
      mockCredentialsManager.initialize.mockResolvedValue();
      mockCookieManager.initialize.mockResolvedValue();

      mockRl.question.mockImplementation((query, callback) => {
        if (query.includes('base URL')) {
          callback('https://test.atlassian.net');
        } else if (query.includes('page ID')) {
          callback('123456');
        }
        return mockRl;
      });

      mockCredentialsManager.getCredentialByBaseUrl.mockReturnValue({
        name: 'Test',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        username: 'testuser',
        token: 'token123',
        puppeteerLogin: false
      });

      mockHttpClient.get.mockResolvedValue({
        data: {
          id: '123456',
          title: 'Test Page',
          version: { number: 1 },
          body: { storage: { value: '<p>old content</p>' } }
        }
      });

      mockHttpClient.put.mockResolvedValue({
        data: { id: '123456', title: 'Test' }
      });
    });

    it('should perform cleanup on successful completion', async () => {
      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockCredentialsManager.cleanup).toHaveBeenCalled();
      expect(mockCookieManager.cleanup).toHaveBeenCalled();
      expect(mockLazyPuppeteerManager.cleanup).toHaveBeenCalled();
      expect(PerformanceUtils.executeCleanup).toHaveBeenCalled();
    });

    it('should log memory usage', async () => {
      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(PerformanceUtils.logMemoryUsage).toHaveBeenCalledWith('cli-start');
      expect(PerformanceUtils.logMemoryUsage).toHaveBeenCalledWith('cli-end');
    });

    it('should handle stdin state correctly', async () => {
      mockProcessStdin.isTTY = true;

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockProcessStdin.setRawMode).toHaveBeenCalledWith(false);
    });

    it('should handle non-TTY stdin', async () => {
      mockProcessStdin.isTTY = false;

      const { main } = await import('../../src/cli/update-confluence-page-cli');
      await main();

      expect(mockProcessStdin.setRawMode).not.toHaveBeenCalled();
    });
  });
});
